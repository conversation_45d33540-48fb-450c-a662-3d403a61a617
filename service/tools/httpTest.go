package tools

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"errors"
	"fmt"
	"fwyytool/api/assistantdeskgo"
	"fwyytool/api/chain"
	"fwyytool/api/userprofile"
	"fwyytool/components"
	"fwyytool/conf"
	"fwyytool/consts"
	toolsInput "fwyytool/controllers/http/tools/input"
	"fwyytool/controllers/http/tools/output"
	jsonLib "fwyytool/libs/json"
	"fwyytool/libs/utils"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	jsoniter "github.com/json-iterator/go"

	"github.com/gin-gonic/gin"
)

var HttpTestService httpTestService

type httpTestService struct {
}

func (s httpTestService) Call(ctx *gin.Context, param toolsInput.HttpTestCallParam) (data output.HttpTestCallOutput, err error) {
	requestID := zlog.GetRequestID(ctx)
	data = output.HttpTestCallOutput{}

	ptrRef, err := s.getRalConfPtrRef(ctx, param.Url)
	if err != nil {
		return
	}
	if ptrRef != nil {
		// 找到了对应的 API 客户端，使用反射调用
		data, err = s.ralCall(ctx, ptrRef, param)
		if err != nil {
			return
		}
	} else {
		// 没有找到对应的 API 客户端，使用直接 HTTP 调用
		data, err = s.httpCall(ctx, param)
		if err != nil {
			return
		}
	}

	data.RequestID = requestID
	_ = s.addTraceReport(ctx, requestID, param.Url)
	return
}

func (s httpTestService) CallForDIff(ctx *gin.Context, param toolsInput.HttpTestCallParam) (data output.HttpTestCallOutput, err error) {
	requestID := zlog.GetRequestID(ctx)
	data = output.HttpTestCallOutput{}

	ptrRef, err := s.getRalConfPtrRefByServiceName(ctx, param.Url)
	if err != nil {
		return
	}
	if ptrRef != nil {
		// 找到了对应的 API 客户端，使用反射调用
		data, err = s.ralCall(ctx, ptrRef, param)
		if err != nil {
			return
		}
	} else {
		// 没有找到对应的 API 客户端，使用直接 HTTP 调用
		data, err = s.httpCall(ctx, param)
		if err != nil {
			return
		}
	}

	if data.Detail.HttpCode != 200 {
		err = errors.New("httpcode not 200, url:" + param.Url)
		return
	}

	data.RequestID = requestID
	return
}

func (s httpTestService) addTraceReport(ctx *gin.Context, requestID string, targetURL string) (err error) {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return
	}
	parsedURL.Path = strings.TrimRight(parsedURL.Path, "/")
	requestIDArr := strings.Split(requestID, ":")
	if len(requestIDArr) == 0 {
		return errors.New("requestIDArr err")
	}
	traceID := requestIDArr[0]

	user, _ := ctx.Get(consts.LOGIN_USER_INFO)
	userInfo := user.(*userprofile.UserInfo)

	_ = assistantdeskgo.NewClient().AddHttpTestTrace(ctx, assistantdeskgo.AddhttpTestTraceParam{
		TraceID:     traceID,
		TargetURL:   parsedURL.Path,
		Operator:    userInfo.Name,
		OperatorUID: int64(userInfo.UserId),
	})
	return
}

func (s httpTestService) GetReportList(ctx *gin.Context, param toolsInput.GetReportListParam) (data output.HttpTestGetReportListOutput, err error) {
	user, _ := ctx.Get(consts.LOGIN_USER_INFO)
	userInfo := user.(*userprofile.UserInfo)

	reportList, err := assistantdeskgo.NewClient().GetReportList(ctx, assistantdeskgo.GetReportListParam{
		OperatorUID: int64(userInfo.UserId),
		Pn:          param.Pn,
		Rn:          param.Rn,
	})
	if err != nil {
		return
	}

	list := make([]*output.TraceReport, 0)
	for _, report := range reportList.List {
		traceReport := chain.TraceReport{}
		_ = jsoniter.UnmarshalFromString(report.TraceReportRaw, &traceReport)
		list = append(list, &output.TraceReport{
			ID:            report.ID,
			TraceID:       report.TraceID,
			TargetURL:     report.TargetURL,
			TraceReport:   traceReport,
			Total:         report.Total,
			TotalDuration: report.TotalDuration,
			RepeatNodeNum: len(traceReport.RepeatNodeReport),
			Status:        report.Status,
			Operator:      report.Operator,
			OperatorUID:   report.OperatorUID,
			CreateTime:    report.CreateTime,
			UpdateTime:    report.UpdateTime,
		})
	}

	tarceUrl := "https://jaeger-docker.zuoyebang.cc/trace/"
	if utils.GetEnvName(nil) != "tips" && utils.GetEnvName(nil) != "online" {
		tarceUrl = "https://shiptrace.zuoyebang.cc/trace/"
	}
	data = output.HttpTestGetReportListOutput{
		TarceUrl: tarceUrl,
		Total:    reportList.Total,
		List:     list,
	}
	return
}

func (s httpTestService) AnalysisTrace(ctx *gin.Context) (err error) {
	zlog.Infof(ctx, "AnalysisTrace start")
	traceReport, err := assistantdeskgo.NewClient().GetUnfinishedReport(ctx)
	if err != nil {
		return
	}

	client := chain.NewClient()

	for _, trace := range traceReport {
		time.Sleep(1 * time.Second) //trace接口性能问题，1s最多只能请求一次
		traceSpanNodeList, apiErr := client.GetTraceDetail(ctx, trace.TraceID)
		if apiErr != nil {
			return
		}

		nodeReport, apiErr := client.AnalysisTrace(ctx, trace.TraceID, trace.TargetURL, traceSpanNodeList)
		if apiErr != nil {
			return
		}

		nodeReportJson, _ := jsoniter.MarshalToString(nodeReport)
		_ = assistantdeskgo.NewClient().UpdateHttpTestTrace(ctx, assistantdeskgo.UpdateHttpTestTraceParam{
			TraceID:       trace.TraceID,
			TraceReport:   nodeReportJson,
			Total:         nodeReport.NodeNum,
			TotalDuration: nodeReport.ApiDuration,
		})
	}

	//fmt.Println(nodeList)
	return
}

func (s httpTestService) getRalConfPtrRef(ctx *gin.Context, targetURL string) (ref *reflect.Value, err error) {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return
	}

	reflectConfValue := reflect.ValueOf(conf.API)
	valueType := reflectConfValue.Type()
	for i := 0; i < valueType.NumField(); i++ {
		apiClientPtr := reflectConfValue.Field(i)
		if apiClientPtr.Kind() == reflect.Ptr && !apiClientPtr.IsNil() {
			apiClient := apiClientPtr.Elem()
			domainVal := apiClient.FieldByName("Domain")
			if domainVal.IsValid() && domainVal.Kind() == reflect.String {
				domain := domainVal.String()
				if strings.Contains(domain, parsedURL.Host) {
					return &apiClientPtr, nil
				}
			}
		}
	}
	return
}

func (s httpTestService) getRalConfPtrRefByServiceName(ctx *gin.Context, targetURL string) (ref *reflect.Value, err error) {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return
	}

	serviceName := components.Util.GetFirstServiceName(parsedURL.Path)
	if serviceName == "" {
		return
	}

	reflectConfValue := reflect.ValueOf(conf.API)
	valueType := reflectConfValue.Type()
	for i := 0; i < valueType.NumField(); i++ {
		apiClientPtr := reflectConfValue.Field(i)
		if apiClientPtr.Kind() == reflect.Ptr && !apiClientPtr.IsNil() {
			apiClient := apiClientPtr.Elem()
			serviceVal := apiClient.FieldByName("Service")
			if serviceVal.IsValid() && serviceVal.Kind() == reflect.String {
				service := serviceVal.String()
				if service == serviceName {
					return &apiClientPtr, nil
				}
			}
		}
	}
	return
}

func (s httpTestService) ralCall(ctx *gin.Context, ptrRef *reflect.Value, param toolsInput.HttpTestCallParam) (data output.HttpTestCallOutput, err error) {
	data = output.HttpTestCallOutput{}

	// 获取 API 客户端实例
	if ptrRef == nil || ptrRef.IsNil() {
		err = errors.New("API client is nil")
		return
	}

	apiClient := ptrRef

	// 准备请求参数（这里不需要单独准备，在 opts 中处理）

	// 准备请求选项
	opts, err := s.prepareHttpRequestOptions(param)
	if err != nil {
		return
	}

	// 解析 URL 获取路径
	parsedURL, err := url.Parse(param.Url)
	if err != nil {
		return
	}
	path := parsedURL.Path
	if parsedURL.RawQuery != "" {
		path += "?" + parsedURL.RawQuery
	}

	// 使用反射调用对应的 HTTP 方法
	var result *reflect.Value
	switch strings.ToUpper(param.Method) {
	case "GET":
		result, err = s.callHttpMethod(apiClient, "HttpGet", ctx, path, opts)
	case "POST":
		result, err = s.callHttpMethod(apiClient, "HttpPost", ctx, path, opts)
	default:
		err = fmt.Errorf("unsupported HTTP method: %s", param.Method)
		return
	}

	if err != nil {
		return
	}

	// 处理返回结果
	data, err = s.processApiResult(ctx, result, param)
	return
}

func (s httpTestService) httpCall(ctx *gin.Context, param toolsInput.HttpTestCallParam) (data output.HttpTestCallOutput, err error) {
	header := http.Header{}
	headerArr := strings.Split(param.Header, "\n")
	for _, headerStr := range headerArr {
		headerDetail := strings.Split(headerStr, ":")
		if len(headerDetail) < 2 {
			continue
		}

		header.Set(headerDetail[0], strings.TrimSpace(strings.Join(headerDetail[1:], ":")))
	}
	header.Add("content-type", param.ContentType)
	header.Add("cookie", param.Cookie)
	header.Add(zlog.TraceHeaderKey, zlog.GetRequestID(ctx))

	respData, err := s.httpDo(ctx, param.Url, param.Method, param.Body, header)
	if err != nil {
		return
	}
	data.Body = respData.Body
	data.Detail.HttpCode = int64(respData.StatusCode)
	data.Detail.Method = respData.Request.Method
	data.Detail.Proto = respData.Request.Proto
	data.Detail.Url = respData.Request.URL.String()

	respHeaderArr := make([]string, 0)
	for k, v := range respData.Header {
		respHeaderArr = append(respHeaderArr, fmt.Sprintf("%s:%s", k, strings.Join(v, "")))
	}
	data.Header, _ = jsonLib.MarshalToString(respHeaderArr)
	return
}

// prepareRequestParams 准备请求参数
func (s httpTestService) prepareRequestParams(param toolsInput.HttpTestCallParam) (map[string]interface{}, error) {
	params := make(map[string]interface{})

	// 如果有 Body 参数，尝试解析
	if param.Body != "" {
		// 根据 ContentType 决定如何处理 Body
		if strings.Contains(param.ContentType, "application/json") {
			// JSON 格式
			var jsonData interface{}
			if err := json.Unmarshal([]byte(param.Body), &jsonData); err != nil {
				return nil, fmt.Errorf("invalid JSON body: %v", err)
			}
			if jsonMap, ok := jsonData.(map[string]interface{}); ok {
				params = jsonMap
			} else {
				params["data"] = jsonData
			}
		} else if strings.Contains(param.ContentType, "application/x-www-form-urlencoded") {
			// Form 格式 - 先尝试解析JSON，然后转换为form data
			var jsonData interface{}
			if err := json.Unmarshal([]byte(param.Body), &jsonData); err != nil {
				return nil, fmt.Errorf("param parse json failed: %v", err)
			} else {
				// JSON解析成功，转换为form data格式
				if jsonMap, ok := jsonData.(map[string]interface{}); ok {
					params = jsonMap
				}
			}
		} else {
			// 其他格式，直接作为字符串
			params["body"] = param.Body
		}
	}

	return params, nil
}

// prepareHttpRequestOptions 准备 HTTP 请求选项
func (s httpTestService) prepareHttpRequestOptions(param toolsInput.HttpTestCallParam) (base.HttpRequestOptions, error) {
	opts := base.HttpRequestOptions{}

	// 设置编码方式
	if strings.Contains(param.ContentType, "application/json") {
		opts.Encode = base.EncodeJson
	} else {
		opts.Encode = base.EncodeForm
	}

	// 设置请求体
	if param.Body != "" {
		requestParams, err := s.prepareRequestParams(param)
		if err != nil {
			return opts, err
		}
		opts.RequestBody = requestParams
	}

	// 设置头部
	if param.Header != "" {
		headers := make(map[string]string)
		headerLines := strings.Split(param.Header, "\n")
		for _, line := range headerLines {
			if parts := strings.SplitN(line, ":", 2); len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				if key != "" && value != "" {
					headers[key] = value
				}
			}
		}
		opts.Headers = headers
	}

	// 设置 Cookie
	if param.Cookie != "" {
		if opts.Headers == nil {
			opts.Headers = make(map[string]string)
		}
		opts.Headers["Cookie"] = param.Cookie
	}

	// 设置 Content-Type
	if param.ContentType != "" {
		if opts.Headers == nil {
			opts.Headers = make(map[string]string)
		}
		opts.Headers["Content-Type"] = param.ContentType
	}

	return opts, nil
}

// callHttpMethod 使用反射调用 HTTP 方法
func (s httpTestService) callHttpMethod(apiClient *reflect.Value, methodName string, ctx *gin.Context, path string, opts base.HttpRequestOptions) (*reflect.Value, error) {
	method := apiClient.MethodByName(methodName)
	if !method.IsValid() {
		return nil, fmt.Errorf("method %s not found", methodName)
	}

	// 准备参数
	ctxValue := reflect.ValueOf(ctx)
	pathValue := reflect.ValueOf(path)
	optsValue := reflect.ValueOf(opts)

	// 调用方法
	results := method.Call([]reflect.Value{ctxValue, pathValue, optsValue})

	// 检查返回值
	if len(results) != 2 {
		return nil, fmt.Errorf("unexpected return values count: %d", len(results))
	}

	// 检查错误
	errValue := results[1]
	if !errValue.IsNil() {
		err := errValue.Interface().(error)
		return nil, err
	}

	// 返回结果
	return &results[0], nil
}

// processApiResult 处理 API 返回结果
func (s httpTestService) processApiResult(ctx *gin.Context, result *reflect.Value, param toolsInput.HttpTestCallParam) (output.HttpTestCallOutput, error) {
	data := output.HttpTestCallOutput{}

	// 获取 ApiResult
	apiResult := result.Interface().(*base.ApiResult)
	if apiResult == nil {
		return data, errors.New("API result is nil")
	}

	// 设置基本信息
	data.Detail.HttpCode = int64(apiResult.HttpCode)
	data.Detail.Method = param.Method
	data.Detail.Url = param.Url
	data.Detail.Proto = "HTTP/1.1" // 默认值

	// 设置响应体
	data.Body = string(apiResult.Response)

	// 设置响应头（ApiResult 没有 Headers 字段，使用默认值）
	respHeaderArr := []string{
		"Content-Type:application/json",
		fmt.Sprintf("Content-Length:%d", len(apiResult.Response)),
	}
	data.Header, _ = jsonLib.MarshalToString(respHeaderArr)

	return data, nil
}

func (s httpTestService) httpDo(ctx *gin.Context, remoteUrl string, method string, queryRaw string, header http.Header) (respData httpResp, err error) {
	respData = httpResp{}
	if len(remoteUrl) == 0 || len(method) == 0 {
		return respData, errors.New("url、method不能为空")
	}
	client := &http.Client{}

	u, err := url.ParseRequestURI(remoteUrl)
	if method == http.MethodGet {
		urlValue, err := url.ParseQuery(queryRaw)
		if err != nil {
			return respData, err
		}
		u.RawQuery = urlValue.Encode()
		queryRaw = ""
	}

	body := io.NopCloser(strings.NewReader(queryRaw))
	req, err := http.NewRequest(method, u.String(), body)
	if err != nil {
		return
	}
	req.Header = header

	resp, err := client.Do(req)
	if err != nil {
		return
	}

	defer resp.Body.Close()

	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	respData.Body = string(b)
	respData.Request = resp.Request
	respData.Header = resp.Header
	respData.StatusCode = resp.StatusCode
	return
}

// Router HTTP代理转发方法
func (s httpTestService) Router(ctx *gin.Context, param toolsInput.HttpTestRouterParam) error {
	// 记录原始请求信息
	zlog.Infof(ctx, "ORIGINAL REQUEST Method: %s", ctx.Request.Method)
	zlog.Infof(ctx, "ORIGINAL REQUEST URL: %s", ctx.Request.URL.String())
	zlog.Infof(ctx, "ORIGINAL REQUEST Headers: %v", ctx.Request.Header)

	// 记录请求参数
	if ctx.Request.URL.RawQuery != "" {
		zlog.Infof(ctx, "Query Params: %s", ctx.Request.URL.RawQuery)
	}

	// 记录请求体
	if ctx.Request.Body != nil {
		bodyBytes, _ := io.ReadAll(ctx.Request.Body)
		if len(bodyBytes) > 0 {
			zlog.Infof(ctx, "Request Body: %s", string(bodyBytes))
			// 恢复请求体，因为已经被读取了
			ctx.Request.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		}
	}

	// 记录代理转发请求的详细信息
	zlog.Infof(ctx, "proxy request - method: %s, target: %s, content-type: %s",
		ctx.Request.Method, param.Domain, ctx.GetHeader("Content-Type"))

	// 解析目标URL
	targetURL, err := url.Parse(param.Domain)
	if err != nil {
		zlog.Errorf(ctx, "invalid target URL: %s, error: %v", param.Domain, err)
		return errors.New("invalid target URL")
	}

	// 验证目标URL的基本信息
	if targetURL.Scheme == "" || targetURL.Host == "" {
		zlog.Errorf(ctx, "incomplete target URL - scheme: %s, host: %s", targetURL.Scheme, targetURL.Host)
		return errors.New("target URL must include scheme and host")
	}

	// 获取原始请求的查询参数，排除domain参数
	originalQuery := ctx.Request.URL.Query()
	originalQuery.Del("domain")

	// 如果目标URL已有查询参数，合并它们
	if targetURL.RawQuery != "" {
		targetQuery, _ := url.ParseQuery(targetURL.RawQuery)
		for key, values := range originalQuery {
			for _, value := range values {
				targetQuery.Add(key, value)
			}
		}
		targetURL.RawQuery = targetQuery.Encode()
	} else {
		targetURL.RawQuery = originalQuery.Encode()
	}

	// 处理请求体内容
	var bodyBytes []byte
	var body io.Reader

	// 检查Content-Type来决定如何处理请求体
	contentType := ctx.GetHeader("Content-Type")
	if ctx.Request.Method == "POST" || ctx.Request.Method == "PUT" || ctx.Request.Method == "PATCH" {
		if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			// 处理form编码的数据
			err := ctx.Request.ParseForm()
			if err != nil {
				return fmt.Errorf("failed to parse form data: %v", err)
			}

			// 将form数据重新编码为请求体
			if len(ctx.Request.Form) > 0 {
				formData := url.Values{}
				for key, values := range ctx.Request.Form {
					// 跳过domain参数，因为它只用于Router识别目标地址
					if key == "domain" {
						continue
					}
					for _, value := range values {
						formData.Add(key, value)
					}
				}
				bodyBytes = []byte(formData.Encode())
				body = bytes.NewReader(bodyBytes)
			}
		} else if strings.Contains(contentType, "multipart/form-data") {
			// 对于multipart/form-data，需要先读取原始请求体，然后再处理
			// 因为ParseMultipartForm会消费Request.Body
			if ctx.Request.Body != nil {
				bodyBytes, err = io.ReadAll(ctx.Request.Body)
				if err != nil {
					return fmt.Errorf("failed to read multipart request body: %v", err)
				}
				if len(bodyBytes) > 0 {
					body = bytes.NewReader(bodyBytes)
				}
			}
		} else {
			// 对于其他类型（JSON等），直接读取原始请求体
			if ctx.Request.Body != nil {
				bodyBytes, err = io.ReadAll(ctx.Request.Body)
				if err != nil {
					return fmt.Errorf("failed to read request body: %v", err)
				}
				if len(bodyBytes) > 0 {
					body = bytes.NewReader(bodyBytes)
				}
			}
		}
	}

	// 创建新的context，避免使用原始请求的context
	req, err := http.NewRequest(ctx.Request.Method, targetURL.String(), body)
	if err != nil {
		return errors.New("failed to create proxy request")
	}

	// 记录转发请求信息
	zlog.Infof(ctx, "FORWARD REQUEST Method: %s", req.Method)
	zlog.Infof(ctx, "FORWARD REQUEST URL: %s", req.URL.String())
	zlog.Infof(ctx, "FORWARD REQUEST Headers: %v", req.Header)

	// 记录转发请求的查询参数
	if req.URL.RawQuery != "" {
		zlog.Infof(ctx, "Forward Query Params: %s", req.URL.RawQuery)
	}

	// 记录转发请求的请求体
	if len(bodyBytes) > 0 {
		zlog.Infof(ctx, "Forward Request Body: %s", string(bodyBytes))
	}

	// 复制所有请求头，但跳过一些可能导致问题的头部
	for key, values := range ctx.Request.Header {
		// 跳过一些可能导致问题的头部
		lowerKey := strings.ToLower(key)
		if lowerKey == "content-length" || lowerKey == "accept-encoding" {
			continue
		}
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// 设置Content-Type和Content-Length
	if len(bodyBytes) > 0 {
		req.ContentLength = int64(len(bodyBytes))
		if contentType != "" {
			req.Header.Set("Content-Type", contentType)
		}
	}

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 3 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		zlog.Errorf(ctx, "proxy request failed: %v", err)
		return fmt.Errorf("proxy request failed: %v", err)
	}
	defer resp.Body.Close()

	// 记录目标服务的响应信息
	zlog.Infof(ctx, "target service response - status: %d, headers: %v", resp.StatusCode, resp.Header)

	// 特殊处理302重定向
	if resp.StatusCode == http.StatusFound {
		location := resp.Header.Get("Location")
		zlog.Warnf(ctx, "target service returned 302 redirect to: %s", location)

		// 记录重定向信息到响应头，便于调试
		ctx.Header("X-Target-Redirect-Location", location)
		ctx.Header("X-Target-Original-Status", "302")
	}

	// 处理响应体，检查是否为gzip压缩
	var responseBody io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to create gzip reader: %v", err)
		}
		defer gzipReader.Close()
		responseBody = gzipReader

		// 移除gzip相关的响应头，因为我们已经解压了
		resp.Header.Del("Content-Encoding")
		resp.Header.Del("Content-Length")
	}

	// 复制响应头
	for key, values := range resp.Header {
		for _, value := range values {
			ctx.Header(key, value)
		}
	}

	// 设置响应状态码
	ctx.Status(resp.StatusCode)

	// 流式复制响应体，添加大小限制（10MB）
	const maxResponseSize = 10 * 1024 * 1024
	limitedReader := io.LimitReader(responseBody, maxResponseSize)

	bytesWritten, err := io.Copy(ctx.Writer, limitedReader)
	if err != nil {
		zlog.Errorf(ctx, "failed to copy response body: %v", err)
		return fmt.Errorf("failed to copy response body: %v", err)
	}

	zlog.Infof(ctx, "proxy response completed - status: %d, bytes: %d", resp.StatusCode, bytesWritten)
	return nil
}
