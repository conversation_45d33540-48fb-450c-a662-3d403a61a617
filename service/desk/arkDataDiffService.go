package desk

import (
	"fwyytool/api/arkgo"

	"github.com/gin-gonic/gin"
)

var ArkDataDiffService arkDataDiffService

type arkDataDiffService struct {
}

func (s arkDataDiffService) GetDiffRes(ctx *gin.Context, timeRange int64, handler string) (data arkgo.GetArkStudentDataDiffResp, err error) {
	arkTestCaseDetail, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, arkgo.GetArkStudentDataDiffResParams{TimeRange: timeRange, Handler: handler})
	if err != nil {
		return
	}
	return arkTestCaseDetail, nil
}

// GetDiffCount 获取昨日差异统计数据
func (s arkDataDiffService) GetDiffCount(ctx *gin.Context) (data map[string]arkgo.GetDataDiffReportOutPut, err error) {
	// 调用arkgo服务获取昨日统计数据
	diffReportData, err := arkgo.NewClient().GetDiffReportRes(ctx)
	if err != nil {
		return nil, err
	}
	return diffReportData, nil
}
