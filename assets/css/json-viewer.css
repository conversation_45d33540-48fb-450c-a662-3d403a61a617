@charset "utf-8";
/*
 * JSON查看器样式
 * 提供JSON数据的美观展示、语法高亮和差异对比功能
 */

/* ===== JSON容器基础样式 ===== */
.json-viewer {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    overflow: auto;
    max-height: 400px;
    position: relative;
}

.json-viewer.expanded {
    max-height: none;
}

/* ===== JSON语法高亮样式 ===== */
.json-string {
    color: #d73a49;
    font-weight: 500;
}

.json-number {
    color: #005cc5;
    font-weight: 600;
}

.json-boolean {
    color: #e36209;
    font-weight: 600;
}

.json-null {
    color: #6f42c1;
    font-weight: 600;
    font-style: italic;
}

.json-key {
    color: #032f62;
    font-weight: 600;
}

.json-punctuation {
    color: #24292e;
}

/* ===== JSON折叠/展开功能 ===== */
.json-collapsible {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-left: 20px;
}

.json-collapsible::before {
    content: '▼';
    position: absolute;
    left: 0;
    top: 0;
    color: #586069;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.json-collapsible.collapsed::before {
    transform: rotate(-90deg);
}

.json-collapsible.collapsed + .json-content {
    display: none;
}

.json-content {
    margin-left: 20px;
    border-left: 2px solid #e1e4e8;
    padding-left: 10px;
}

/* ===== 差异对比样式 ===== */
.json-diff-container {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.json-diff-panel {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.json-diff-header {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
    color: white;
    padding: 12px 15px;
    font-weight: 600;
    font-size: 14px;
}

.json-diff-header.old-data {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.json-diff-header.new-data {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.json-diff-content {
    padding: 15px;
    max-height: 300px;
    overflow: auto;
}

/* ===== 差异高亮样式 ===== */
.json-added {
    background-color: #d4edda;
    color: #155724;
    padding: 2px 4px;
    border-radius: 3px;
    border-left: 3px solid #28a745;
}

.json-removed {
    background-color: #f8d7da;
    color: #721c24;
    padding: 2px 4px;
    border-radius: 3px;
    border-left: 3px solid #dc3545;
    text-decoration: line-through;
}

.json-modified {
    background-color: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 3px;
    border-left: 3px solid #ffc107;
}

.json-unchanged {
    opacity: 0.7;
}

/* ===== JSON工具栏 ===== */
.json-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 6px 6px 0 0;
}

.json-toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.json-toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

.json-btn {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.json-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.json-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.json-btn.copy-btn {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.json-btn.copy-btn:hover {
    background: #218838;
    border-color: #1e7e34;
}

/* ===== JSON搜索功能 ===== */
.json-search {
    position: relative;
    margin-bottom: 10px;
}

.json-search-input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
}

.json-search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.json-search-result {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 600;
}

.json-search-current {
    background-color: #ffc107;
    color: #212529;
}

/* ===== JSON行号 ===== */
.json-with-lines {
    display: flex;
}

.json-line-numbers {
    background: #f6f8fa;
    color: #586069;
    padding: 15px 10px;
    border-right: 1px solid #e1e4e8;
    font-size: 12px;
    line-height: 1.6;
    user-select: none;
    min-width: 40px;
    text-align: right;
}

.json-code-content {
    flex: 1;
    padding: 15px;
    overflow: auto;
}

/* ===== JSON格式化选项 ===== */
.json-format-options {
    display: flex;
    gap: 15px;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.json-format-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

.json-format-option input[type="checkbox"] {
    margin: 0;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .json-diff-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .json-toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .json-toolbar-left,
    .json-toolbar-right {
        justify-content: center;
    }
    
    .json-viewer {
        font-size: 12px;
        padding: 10px;
    }
    
    .json-diff-content {
        max-height: 200px;
    }
}

@media (max-width: 480px) {
    .json-viewer {
        font-size: 11px;
        padding: 8px;
    }
    
    .json-btn {
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .json-diff-content {
        max-height: 150px;
        padding: 10px;
    }
    
    .json-format-options {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* ===== 动画效果 ===== */
.json-fade-in {
    animation: jsonFadeIn 0.3s ease-in-out;
}

@keyframes jsonFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.json-slide-down {
    animation: jsonSlideDown 0.3s ease-in-out;
}

@keyframes jsonSlideDown {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 400px;
        opacity: 1;
    }
}

/* ===== 复制成功提示 ===== */
.json-copy-success {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    animation: jsonCopySuccess 2s ease-in-out;
}

@keyframes jsonCopySuccess {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

/* ===== 错误状态样式 ===== */
.json-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 15px;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
}

.json-error-title {
    font-weight: 600;
    margin-bottom: 8px;
}

.json-error-message {
    font-size: 12px;
    opacity: 0.8;
}
