@charset "utf-8";
/*
 * 数据差异页面增强样式
 * 提供现代化的页面布局和响应式设计
 */

/* ===== 页面整体布局优化 ===== */
.datadiff-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.datadiff-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.datadiff-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

/* ===== 表单样式优化 ===== */
.datadiff-form {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.datadiff-form .form-group {
    margin-bottom: 20px;
}

.datadiff-form label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.datadiff-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 10px 15px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.datadiff-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.datadiff-form .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 600;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.datadiff-form .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* ===== 统计卡片样式 ===== */
.stats-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.stats-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.stats-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    border: none;
}

.stats-table td {
    padding: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    border: none;
    background: white;
}

.stats-table .stat-total {
    color: #28a745;
}

.stats-table .stat-diff {
    color: #dc3545;
}

.stats-table .stat-no-diff {
    color: #28a745;
}

.stats-table .stat-unfinished {
    color: #ffc107;
}

/* ===== 昨日小计样式 ===== */
.yesterday-summary {
    margin-bottom: 20px;
}

.yesterday-summary-link {
    display: inline-block;
    color: #dc3545 !important;
    font-size: 18px;
    font-weight: bold;
    text-decoration: none;
    padding: 10px 20px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.yesterday-summary-link:hover {
    color: #c82333 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.yesterday-data {
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: none;
}

/* ===== 标签页样式优化 ===== */
.datadiff-tabs {
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.datadiff-tabs .nav-tabs {
    border-bottom: none;
    margin: 0;
}

.datadiff-tabs .nav-tabs > li {
    margin-bottom: 0;
}

.datadiff-tabs .nav-tabs > li > a {
    border: none;
    border-radius: 0;
    padding: 15px 25px;
    color: #495057;
    font-weight: 600;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.datadiff-tabs .nav-tabs > li.active > a {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 3px solid #28a745;
}

/* ===== 数据表格样式优化 ===== */
.datadiff-table-container {
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.datadiff-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
    font-size: 14px;
}

.datadiff-table th {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.datadiff-table td {
    padding: 15px 10px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
    background: white;
}

.datadiff-table tr:hover td {
    background-color: #f8f9fa;
}

/* ===== JSON文本域样式优化 ===== */
.json-textarea {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    background-color: #f8f9fa;
    resize: both;
    min-width: 200px;
    min-height: 100px;
    max-width: 100%;
    width: 100%;
    height: 200px;
    box-sizing: border-box;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.json-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
    background-color: white;
}

/* ===== 差异链接样式 ===== */
.diff-link {
    color: #dc3545 !important;
    font-weight: 600;
    text-decoration: none;
    padding: 6px 12px;
    background: #fff5f5;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.diff-link:hover {
    color: #c82333 !important;
    background: #f8d7da;
    border-color: #f1b0b7;
    text-decoration: none;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .datadiff-container {
        padding: 15px;
    }
    
    .datadiff-table th,
    .datadiff-table td {
        padding: 10px 8px;
        font-size: 13px;
    }
    
    .json-textarea {
        height: 150px;
        min-width: 150px;
    }
}

@media (max-width: 768px) {
    .datadiff-container {
        padding: 10px;
    }
    
    .datadiff-header {
        padding: 15px;
    }
    
    .datadiff-header h3 {
        font-size: 20px;
    }
    
    .datadiff-form {
        padding: 20px;
    }
    
    .datadiff-table-container {
        overflow-x: auto;
    }
    
    .datadiff-table {
        min-width: 800px;
    }
    
    .json-textarea {
        height: 120px;
        min-width: 120px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .datadiff-container {
        padding: 5px;
    }
    
    .datadiff-header,
    .datadiff-form {
        padding: 15px;
    }
    
    .stats-table th,
    .stats-table td {
        padding: 10px 5px;
        font-size: 12px;
    }
    
    .datadiff-table th,
    .datadiff-table td {
        padding: 8px 5px;
        font-size: 12px;
    }
}

/* ===== 加载状态样式 ===== */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 错误提示样式 ===== */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 20px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    margin: 10px 0;
    font-weight: 500;
}

/* ===== 增强错误容器样式 ===== */
.enhanced-error {
    background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);
    border: 2px solid #f5c6cb;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 6px rgba(220, 53, 69, 0.1);
}

.error-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.error-icon {
    font-size: 20px;
    margin-right: 10px;
}

.error-title {
    font-size: 16px;
    font-weight: 600;
    color: #721c24;
}

.error-details {
    background: #ffffff;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
    font-size: 14px;
    color: #721c24;
}

.error-suggestions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
    color: #856404;
}

.error-suggestions ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.error-suggestions li {
    margin: 4px 0;
    font-size: 14px;
}

.error-meta {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #f5c6cb;
    color: #6c757d;
    font-size: 12px;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 12px 20px;
    border-radius: 6px;
    border: 1px solid #c3e6cb;
    margin: 10px 0;
    font-weight: 500;
}

/* ===== 表格增强器样式 ===== */
.table-enhancer-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.table-enhancer-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-search-container {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.table-search-input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.table-search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.table-search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

.table-info-container {
    color: #6c757d;
    font-size: 14px;
}

/* ===== 表格排序样式 ===== */
.table-sticky-header thead th {
    position: sticky;
    top: 0;
    z-index: 10;
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
}

.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sort-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.sort-indicator::after {
    content: '↕';
    font-size: 12px;
}

.sort-indicator.asc::after {
    content: '↑';
    opacity: 1;
}

.sort-indicator.desc::after {
    content: '↓';
    opacity: 1;
}

/* ===== 分页样式 ===== */
.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    gap: 5px;
}

.pagination-btn {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 40px;
}

.pagination-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 6px 8px;
    color: #6c757d;
}

/* ===== 数据加载管理样式 ===== */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 6px;
}

.loading-content {
    text-align: center;
    color: #495057;
}

.loading-message {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
}

.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* ===== 通知样式 ===== */
.datadiff-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
}

.datadiff-notification.show {
    transform: translateX(0);
}

.datadiff-notification.info {
    background: #17a2b8;
}

.datadiff-notification.success {
    background: #28a745;
}

.datadiff-notification.error {
    background: #dc3545;
}

.datadiff-notification.warning {
    background: #ffc107;
    color: #212529;
}

/* ===== JSON文本域工具栏 ===== */
.json-textarea-toolbar {
    display: flex;
    gap: 5px;
    margin-bottom: 5px;
    justify-content: flex-end;
}

.json-textarea-toolbar .json-btn {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.json-textarea-toolbar .json-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.json-textarea-toolbar .copy-btn {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.json-textarea-toolbar .copy-btn:hover {
    background: #218838;
    border-color: #1e7e34;
}

/* ===== 昨日统计表格样式 ===== */
.yesterday-stats-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.yesterday-stats-table th {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
    color: white;
    padding: 12px 10px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
}

.yesterday-stats-table td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    font-size: 13px;
}

.yesterday-stats-table tr.even {
    background: #f8f9fa;
}

.yesterday-stats-table tr:hover {
    background: #e9ecef;
}

.yesterday-stats-table .has-diff {
    color: #dc3545;
    font-weight: 600;
}

/* ===== 错误容器样式 ===== */
.error-container {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.retry-btn {
    margin-top: 10px;
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.retry-btn:hover {
    background: #0056b3;
    border-color: #004085;
}

/* ===== 提交按钮加载状态 ===== */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== 点击效果 ===== */
.clicking {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* ===== JSON错误样式 ===== */
.json-error {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #495057;
    max-height: 300px;
    overflow: auto;
}

/* ===== 增强的差异显示样式 ===== */
.has-differences {
    background-color: #fff5f5 !important;
    border-left: 3px solid #dc3545;
}

.has-differences:hover {
    background-color: #ffeaea !important;
}

.diff-indicator {
    margin-right: 5px;
    font-size: 14px;
}

.diff-count.has-diff {
    color: #dc3545;
    font-weight: 700;
    background: #fff5f5;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}

/* ===== JSON文本域增强样式 ===== */
.enhanced-json {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.enhanced-json:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.enhanced-json:hover {
    border-color: #adb5bd;
}

.non-json {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* ===== 加载状态样式 ===== */
body.loading {
    cursor: wait;
}

body.loading * {
    pointer-events: none;
}

.loading-icon {
    display: inline-block;
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== 工具提示增强 ===== */
[title] {
    position: relative;
    cursor: help;
}

/* ===== 键盘快捷键提示 ===== */
.keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.keyboard-shortcuts.show {
    opacity: 1;
}

.keyboard-shortcuts kbd {
    background: #495057;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin: 0 2px;
}

/* ===== 表单标题样式 ===== */
.form-title {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* ===== 表单行样式 ===== */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.form-row .form-group {
    flex: 1;
    min-width: 0; /* 防止flex项目溢出 */
}

.form-row .form-group label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.form-actions {
    text-align: center;
    margin-top: 20px;
}

/* ===== JSON单元格样式 ===== */
.json-cell {
    position: relative;
    max-width: 250px;
}

.json-cell .json-textarea {
    width: 100%;
    min-height: 120px;
    max-height: 200px;
    resize: vertical;
}

/* ===== 处理器名称样式 ===== */
.handler-name {
    font-weight: 600;
    color: #495057;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
}

/* ===== 时间单元格样式 ===== */
.time-cell {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
}

/* ===== 差异链接单元格样式 ===== */
.diff-link-cell {
    text-align: center;
    vertical-align: middle;
}

/* ===== 基本搜索样式 ===== */
.basic-search-container {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.basic-search-container input {
    width: 100% !important;
    max-width: 400px;
    padding: 10px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 6px !important;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.basic-search-container input:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    outline: none !important;
}

/* ===== 响应式优化 ===== */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-row .form-group {
        width: 100%;
    }

    .json-cell {
        max-width: 200px;
    }

    .json-cell .json-textarea {
        min-height: 100px;
        max-height: 150px;
        font-size: 11px;
    }

    .keyboard-shortcuts {
        display: none;
    }
}
