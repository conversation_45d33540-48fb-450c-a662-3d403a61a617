/**
 * 数据差异页面增强模块
 * 提供加载状态管理、错误处理、AJAX优化和用户交互增强
 */

(function(window, $) {
    'use strict';

    // 数据差异增强器类
    function DataDiffEnhancer() {
        this.options = {
            loadingText: '加载中...',
            errorRetryText: '重试',
            debounceDelay: 300,
            throttleDelay: 100,
            maxRetries: 3,
            retryDelay: 1000,
            enableNotifications: true
        };
        this.loadingStates = new Map();
        this.retryCount = 0;
    }

    DataDiffEnhancer.prototype = {
        /**
         * 初始化增强功能
         * @param {object} options - 配置选项
         */
        init: function(options) {
            this.options = $.extend({}, this.options, options);
            this._setupGlobalHandlers();
            this._enhanceExistingElements();
            return this;
        },

        /**
         * 设置全局处理器
         */
        _setupGlobalHandlers: function() {
            var self = this;
            
            // 全局AJAX错误处理
            $(document).ajaxError(function(event, xhr, settings, error) {
                self._handleAjaxError(xhr, settings, error);
            });
            
            // 全局AJAX开始处理
            $(document).ajaxStart(function() {
                self._showGlobalLoading();
            });
            
            // 全局AJAX完成处理
            $(document).ajaxStop(function() {
                self._hideGlobalLoading();
            });
        },

        /**
         * 增强现有元素
         */
        _enhanceExistingElements: function() {
            this._enhanceYesterdayToggle();
            this._enhanceFormSubmission();
            this._enhanceTableInteraction();
        },

        /**
         * 增强昨日小计切换功能
         */
        _enhanceYesterdayToggle: function() {
            var self = this;
            var originalToggleData = window.toggleData;
            
            if (typeof originalToggleData === 'function') {
                window.toggleData = function(event) {
                    event.preventDefault();
                    
                    var $link = $(event.target);
                    var $dataSpan = $('#data');
                    
                    // 如果数据区域当前可见，则隐藏它
                    if ($dataSpan.is(':visible')) {
                        $dataSpan.slideUp(300);
                        return;
                    }
                    
                    // 如果数据已经加载过，则仅显示它
                    if (window.dataLoaded) {
                        $dataSpan.slideDown(300);
                        return;
                    }
                    
                    // 显示加载状态
                    self.showLoading($dataSpan, '正在获取昨日统计数据...');
                    
                    // 使用增强的AJAX请求
                    self.enhancedAjax({
                        url: '/fwyytool/arkgo/tool/getdiffcount',
                        method: 'GET',
                        timeout: 10000,
                        success: function(data) {
                            self._processYesterdayData(data, $dataSpan);
                            window.dataLoaded = true;
                        },
                        error: function(xhr, status, error) {
                            self._handleYesterdayDataError(xhr, $dataSpan);
                        },
                        complete: function() {
                            self.hideLoading($dataSpan);
                        }
                    });
                };
            }
        },

        /**
         * 增强表单提交
         */
        _enhanceFormSubmission: function() {
            var self = this;
            
            $('form.form-inline').on('submit', function(event) {
                var $form = $(this);
                var $submitBtn = $form.find('button[type="submit"]');
                
                // 防止重复提交
                if ($submitBtn.prop('disabled')) {
                    event.preventDefault();
                    return false;
                }
                
                // 显示提交状态
                self._showSubmitLoading($submitBtn);
                
                // 设置超时自动恢复
                setTimeout(function() {
                    self._hideSubmitLoading($submitBtn);
                }, 30000);
            });
        },

        /**
         * 增强表格交互
         */
        _enhanceTableInteraction: function() {
            var self = this;
            
            // 为JSON文本域添加增强功能
            $('.taskList textarea').each(function() {
                self._enhanceJsonTextarea($(this));
            });
            
            // 为差异链接添加增强功能
            $('.taskList a[target="_blank"]').each(function() {
                self._enhanceDiffLink($(this));
            });
        },

        /**
         * 增强JSON文本域
         */
        _enhanceJsonTextarea: function($textarea) {
            var self = this;
            var originalContent = $textarea.val();
            
            // 添加工具栏
            var $toolbar = $('<div class="json-textarea-toolbar">' +
                '<button type="button" class="json-btn format-btn">格式化</button>' +
                '<button type="button" class="json-btn copy-btn">复制</button>' +
                '<button type="button" class="json-btn expand-btn">展开</button>' +
                '</div>');
            
            $textarea.before($toolbar);
            
            // 绑定工具栏事件
            $toolbar.on('click', '.format-btn', function() {
                self._formatJsonTextarea($textarea);
            });
            
            $toolbar.on('click', '.copy-btn', function() {
                self._copyTextareaContent($textarea);
            });
            
            $toolbar.on('click', '.expand-btn', function() {
                self._toggleTextareaSize($textarea);
            });
        },

        /**
         * 增强差异链接
         */
        _enhanceDiffLink: function($link) {
            var self = this;
            
            $link.on('click', function(event) {
                var $this = $(this);
                
                // 添加点击效果
                $this.addClass('clicking');
                setTimeout(function() {
                    $this.removeClass('clicking');
                }, 200);
                
                // 显示加载提示
                if (self.options.enableNotifications) {
                    self.showNotification('正在打开差异详情...', 'info', 2000);
                }
            });
        },

        /**
         * 增强的AJAX请求
         */
        enhancedAjax: function(options) {
            var self = this;
            var defaultOptions = {
                timeout: 15000,
                retries: this.options.maxRetries,
                retryDelay: this.options.retryDelay
            };

            options = $.extend({}, defaultOptions, options);

            function makeRequest(retryCount) {
                return $.ajax(options).fail(function(xhr, status, error) {
                    if (retryCount < options.retries && (status === 'timeout' || xhr.status >= 500)) {
                        setTimeout(function() {
                            makeRequest(retryCount + 1);
                        }, options.retryDelay * (retryCount + 1));
                    } else if (options.error) {
                        options.error(xhr, status, error);
                    }
                });
            }

            return makeRequest(0);
        },

        /**
         * 获取差异数据API
         */
        getDiffDataAPI: function(params, callback) {
            var self = this;

            return this.enhancedAjax({
                url: '/desk/datadiff/api',
                method: 'GET',
                data: params,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        if (callback && typeof callback === 'function') {
                            callback(null, response);
                        }
                    } else {
                        var error = new Error(response.error.message || '请求失败');
                        if (callback && typeof callback === 'function') {
                            callback(error, null);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    var errorMsg = '网络请求失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error.message;
                    }

                    var err = new Error(errorMsg);
                    if (callback && typeof callback === 'function') {
                        callback(err, null);
                    }
                }
            });
        },

        /**
         * 刷新表格数据
         */
        refreshTableData: function(params) {
            var self = this;
            var $table = $('#diffDataTable');
            var $tbody = $table.find('tbody');

            // 显示加载状态
            var loadingId = this.showLoading($table, '正在刷新数据...');

            this.getDiffDataAPI(params, function(error, response) {
                self.hideLoading($table, loadingId);

                if (error) {
                    self.showNotification('刷新数据失败: ' + error.message, 'error', 5000);
                    return;
                }

                // 更新表格内容
                self._updateTableContent($tbody, response.data);

                // 更新分页信息
                self._updatePaginationInfo(response.pagination);

                // 显示成功提示
                self.showNotification('数据刷新成功', 'success', 2000);
            });
        },

        /**
         * 更新表格内容
         */
        _updateTableContent: function($tbody, data) {
            $tbody.empty();

            if (!data || !data.DataDiffList || data.DataDiffList.length === 0) {
                $tbody.append('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
                return;
            }

            // 这里需要根据实际的数据结构来渲染表格行
            // 由于我们不知道确切的数据结构，这里提供一个框架
            data.DataDiffList.forEach(function(item) {
                var row = self._buildTableRow(item);
                $tbody.append(row);
            });
        },

        /**
         * 构建表格行
         */
        _buildTableRow: function(item) {
            // 这里需要根据实际的数据结构来构建行
            // 返回jQuery对象或HTML字符串
            return '<tr><td colspan="8">数据行构建中...</td></tr>';
        },

        /**
         * 更新分页信息
         */
        _updatePaginationInfo: function(pagination) {
            // 更新分页控件
            var info = '显示 ' + ((pagination.page - 1) * pagination.pageSize + 1) +
                      '-' + Math.min(pagination.page * pagination.pageSize, pagination.total) +
                      ' 条，共 ' + pagination.total + ' 条';

            $('.table-info').text(info);
        },

        /**
         * 显示加载状态
         */
        showLoading: function($container, message) {
            var loadingId = this._generateLoadingId();
            message = message || this.options.loadingText;
            
            var $loading = $('<div class="loading-overlay" data-loading-id="' + loadingId + '">' +
                '<div class="loading-content">' +
                '<div class="loading-spinner"></div>' +
                '<div class="loading-message">' + message + '</div>' +
                '</div>' +
                '</div>');
            
            $container.css('position', 'relative').append($loading);
            this.loadingStates.set(loadingId, $loading);
            
            return loadingId;
        },

        /**
         * 隐藏加载状态
         */
        hideLoading: function($container, loadingId) {
            if (loadingId) {
                var $loading = this.loadingStates.get(loadingId);
                if ($loading) {
                    $loading.fadeOut(200, function() {
                        $loading.remove();
                    });
                    this.loadingStates.delete(loadingId);
                }
            } else {
                $container.find('.loading-overlay').fadeOut(200, function() {
                    $(this).remove();
                });
            }
        },

        /**
         * 显示通知
         */
        showNotification: function(message, type, duration) {
            if (!this.options.enableNotifications) return;
            
            type = type || 'info';
            duration = duration || 3000;
            
            var $notification = $('<div class="datadiff-notification ' + type + '">' + message + '</div>');
            
            $('body').append($notification);
            
            // 显示动画
            $notification.addClass('show');
            
            // 自动隐藏
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, duration);
        },

        /**
         * 防抖函数
         */
        debounce: function(func, delay) {
            var timeoutId;
            delay = delay || this.options.debounceDelay;
            
            return function() {
                var context = this;
                var args = arguments;
                
                clearTimeout(timeoutId);
                timeoutId = setTimeout(function() {
                    func.apply(context, args);
                }, delay);
            };
        },

        /**
         * 节流函数
         */
        throttle: function(func, delay) {
            var lastCall = 0;
            delay = delay || this.options.throttleDelay;
            
            return function() {
                var now = Date.now();
                if (now - lastCall >= delay) {
                    lastCall = now;
                    return func.apply(this, arguments);
                }
            };
        },

        // 私有方法
        _generateLoadingId: function() {
            return 'loading_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        _showGlobalLoading: function() {
            if (!$('.global-loading').length) {
                $('body').append('<div class="global-loading"><div class="loading-spinner"></div></div>');
            }
        },

        _hideGlobalLoading: function() {
            $('.global-loading').remove();
        },

        _showSubmitLoading: function($btn) {
            var originalText = $btn.text();
            $btn.data('original-text', originalText)
                .text('提交中...')
                .prop('disabled', true)
                .addClass('loading');
        },

        _hideSubmitLoading: function($btn) {
            var originalText = $btn.data('original-text') || '查询';
            $btn.text(originalText)
                .prop('disabled', false)
                .removeClass('loading');
        },

        _processYesterdayData: function(data, $container) {
            // 复用原有的数据处理逻辑，但添加动画效果
            if (data.errNo === 0 && data.data) {
                var statsData = data.data;
                var sortableData = Object.entries(statsData).map(([key, value]) => ({
                    name: key,
                    ...value
                }));
                
                sortableData.sort((a, b) => b.hasDiffCnt - a.hasDiffCnt);
                
                var tableHtml = this._buildYesterdayTable(sortableData);
                $container.html(tableHtml).hide().slideDown(300);
            } else {
                var formattedJSON = JSON.stringify(data, null, 4);
                $container.html('<pre class="json-error">' + formattedJSON + '</pre>').hide().slideDown(300);
            }
        },

        _buildYesterdayTable: function(data) {
            var html = '<table class="yesterday-stats-table">';
            html += '<thead><tr>';
            html += '<th>接口名称</th><th>有差异</th><th>无差异</th><th>未完成</th><th>失败</th>';
            html += '</tr></thead><tbody>';
            
            data.forEach((item, index) => {
                var rowClass = index % 2 === 0 ? 'even' : 'odd';
                var diffClass = item.hasDiffCnt > 0 ? 'has-diff' : '';
                
                html += `<tr class="${rowClass}">`;
                html += `<td>${item.name}</td>`;
                html += `<td class="${diffClass}">${item.hasDiffCnt}</td>`;
                html += `<td>${item.noDiffCnt}</td>`;
                html += `<td>${item.unFinishCnt}</td>`;
                html += `<td>${item.failedCnt}</td>`;
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            return html;
        },

        _handleYesterdayDataError: function(xhr, $container) {
            var errorMsg = '获取数据失败';
            if (xhr.status === 0) {
                errorMsg = '网络连接失败，请检查网络连接';
            } else if (xhr.status >= 500) {
                errorMsg = '服务器错误，请稍后重试';
            } else if (xhr.status === 404) {
                errorMsg = '请求的资源不存在';
            }
            
            var retryHtml = '<div class="error-container">' +
                '<div class="error-message">' + errorMsg + '</div>' +
                '<button class="json-btn retry-btn" onclick="toggleData(event)">重试</button>' +
                '</div>';
            
            $container.html(retryHtml).hide().slideDown(300);
        },

        _handleAjaxError: function(xhr, settings, error) {
            if (this.options.enableNotifications) {
                var message = '请求失败: ' + (error || '未知错误');
                this.showNotification(message, 'error', 5000);
            }
        },

        _formatJsonTextarea: function($textarea) {
            try {
                var content = $textarea.val();
                var parsed = JSON.parse(content);
                var formatted = JSON.stringify(parsed, null, 2);
                $textarea.val(formatted);
                this.showNotification('JSON格式化成功', 'success', 2000);
            } catch (e) {
                this.showNotification('JSON格式错误: ' + e.message, 'error', 3000);
            }
        },

        _copyTextareaContent: function($textarea) {
            $textarea.select();
            document.execCommand('copy');
            this.showNotification('内容已复制到剪贴板', 'success', 2000);
        },

        _toggleTextareaSize: function($textarea) {
            var currentHeight = $textarea.height();
            var newHeight = currentHeight > 200 ? 200 : 400;
            $textarea.animate({ height: newHeight }, 300);
        }
    };

    // 全局实例
    window.DataDiffEnhancer = DataDiffEnhancer;
    
    // 自动初始化
    $(document).ready(function() {
        if (window.location.pathname.includes('datadiff/diffdetail')) {
            var enhancer = new DataDiffEnhancer();
            enhancer.init();
            window.dataDiffEnhancer = enhancer;
        }
    });

})(window, window.jQuery);
