/**
 * JSON格式化模块
 * 提供JSON数据格式化、语法高亮、差异对比和交互功能
 */

(function(window, $) {
    'use strict';

    // JSON格式化器类
    function JsonFormatter() {
        this.options = {
            theme: 'default',
            collapsed: 2,
            withQuotes: true,
            withLinks: false,
            showLineNumbers: false,
            enableSearch: true,
            enableCopy: true
        };
    }

    JsonFormatter.prototype = {
        /**
         * 格式化JSON数据并显示
         * @param {string|object} jsonData - JSON数据
         * @param {string|jQuery} container - 容器选择器或jQuery对象
         * @param {object} options - 配置选项
         */
        format: function(jsonData, container, options) {
            var self = this;
            var $container = $(container);
            var opts = $.extend({}, this.options, options);
            
            try {
                var data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
                var formattedHtml = this._buildJsonHtml(data, opts);
                
                $container.html(this._buildContainer(formattedHtml, opts));
                this._bindEvents($container, opts);
                
                return true;
            } catch (error) {
                this._showError($container, error.message);
                return false;
            }
        },

        /**
         * 比较两个JSON对象并高亮差异
         * @param {string|object} oldData - 旧数据
         * @param {string|object} newData - 新数据
         * @param {string|jQuery} container - 容器选择器或jQuery对象
         * @param {object} options - 配置选项
         */
        compare: function(oldData, newData, container, options) {
            var self = this;
            var $container = $(container);
            var opts = $.extend({}, this.options, options);
            
            try {
                var oldObj = typeof oldData === 'string' ? JSON.parse(oldData) : oldData;
                var newObj = typeof newData === 'string' ? JSON.parse(newData) : newData;
                
                var diffResult = this._compareObjects(oldObj, newObj);
                var compareHtml = this._buildCompareHtml(oldObj, newObj, diffResult, opts);
                
                $container.html(compareHtml);
                this._bindEvents($container, opts);
                
                return diffResult;
            } catch (error) {
                this._showError($container, error.message);
                return null;
            }
        },

        /**
         * 构建JSON HTML结构
         */
        _buildJsonHtml: function(data, opts, level) {
            level = level || 0;
            var html = '';
            var indent = '  '.repeat(level);
            
            if (Array.isArray(data)) {
                html += '<span class="json-punctuation">[</span>';
                if (data.length > 0) {
                    html += this._buildCollapsible(level);
                    html += '<div class="json-content">';
                    for (var i = 0; i < data.length; i++) {
                        html += '\n' + indent + '  ';
                        html += this._buildJsonHtml(data[i], opts, level + 1);
                        if (i < data.length - 1) {
                            html += '<span class="json-punctuation">,</span>';
                        }
                    }
                    html += '\n' + indent;
                    html += '</div>';
                }
                html += '<span class="json-punctuation">]</span>';
            } else if (typeof data === 'object' && data !== null) {
                html += '<span class="json-punctuation">{</span>';
                var keys = Object.keys(data);
                if (keys.length > 0) {
                    html += this._buildCollapsible(level);
                    html += '<div class="json-content">';
                    for (var i = 0; i < keys.length; i++) {
                        var key = keys[i];
                        html += '\n' + indent + '  ';
                        html += '<span class="json-key">"' + this._escapeHtml(key) + '"</span>';
                        html += '<span class="json-punctuation">: </span>';
                        html += this._buildJsonHtml(data[key], opts, level + 1);
                        if (i < keys.length - 1) {
                            html += '<span class="json-punctuation">,</span>';
                        }
                    }
                    html += '\n' + indent;
                    html += '</div>';
                }
                html += '<span class="json-punctuation">}</span>';
            } else {
                html += this._formatValue(data);
            }
            
            return html;
        },

        /**
         * 构建容器HTML
         */
        _buildContainer: function(content, opts) {
            var html = '<div class="json-viewer">';
            
            if (opts.enableSearch || opts.enableCopy) {
                html += '<div class="json-toolbar">';
                html += '<div class="json-toolbar-left">';
                
                if (opts.enableSearch) {
                    html += '<div class="json-search">';
                    html += '<input type="text" class="json-search-input" placeholder="搜索...">';
                    html += '<span class="json-search-icon">🔍</span>';
                    html += '</div>';
                }
                
                html += '</div>';
                html += '<div class="json-toolbar-right">';
                
                if (opts.enableCopy) {
                    html += '<button class="json-btn copy-btn" data-action="copy">复制</button>';
                }
                
                html += '<button class="json-btn" data-action="expand-all">展开全部</button>';
                html += '<button class="json-btn" data-action="collapse-all">折叠全部</button>';
                html += '</div>';
                html += '</div>';
            }
            
            if (opts.showLineNumbers) {
                html += '<div class="json-with-lines">';
                html += '<div class="json-line-numbers"></div>';
                html += '<div class="json-code-content">' + content + '</div>';
                html += '</div>';
            } else {
                html += '<div class="json-code-content">' + content + '</div>';
            }
            
            html += '</div>';
            return html;
        },

        /**
         * 构建对比HTML
         */
        _buildCompareHtml: function(oldData, newData, diffResult, opts) {
            var html = '<div class="json-diff-container">';
            
            html += '<div class="json-diff-panel">';
            html += '<div class="json-diff-header old-data">旧数据 (Before)</div>';
            html += '<div class="json-diff-content">';
            html += this._buildJsonHtml(oldData, opts);
            html += '</div>';
            html += '</div>';
            
            html += '<div class="json-diff-panel">';
            html += '<div class="json-diff-header new-data">新数据 (After)</div>';
            html += '<div class="json-diff-content">';
            html += this._buildJsonHtml(newData, opts);
            html += '</div>';
            html += '</div>';
            
            html += '</div>';
            return html;
        },

        /**
         * 构建折叠控制器
         */
        _buildCollapsible: function(level) {
            return '<span class="json-collapsible" data-level="' + level + '"></span>';
        },

        /**
         * 格式化值
         */
        _formatValue: function(value) {
            if (typeof value === 'string') {
                return '<span class="json-string">"' + this._escapeHtml(value) + '"</span>';
            } else if (typeof value === 'number') {
                return '<span class="json-number">' + value + '</span>';
            } else if (typeof value === 'boolean') {
                return '<span class="json-boolean">' + value + '</span>';
            } else if (value === null) {
                return '<span class="json-null">null</span>';
            } else {
                return '<span class="json-undefined">undefined</span>';
            }
        },

        /**
         * 比较两个对象
         */
        _compareObjects: function(obj1, obj2, path) {
            path = path || '';
            var differences = [];
            
            // 简化的差异检测逻辑
            if (JSON.stringify(obj1) !== JSON.stringify(obj2)) {
                differences.push({
                    path: path,
                    type: 'modified',
                    oldValue: obj1,
                    newValue: obj2
                });
            }
            
            return differences;
        },

        /**
         * 绑定事件
         */
        _bindEvents: function($container, opts) {
            var self = this;
            
            // 折叠/展开事件
            $container.on('click', '.json-collapsible', function() {
                $(this).toggleClass('collapsed');
            });
            
            // 工具栏按钮事件
            $container.on('click', '.json-btn', function() {
                var action = $(this).data('action');
                self._handleToolbarAction(action, $container);
            });
            
            // 搜索事件
            $container.on('input', '.json-search-input', function() {
                var searchTerm = $(this).val();
                self._performSearch(searchTerm, $container);
            });
        },

        /**
         * 处理工具栏操作
         */
        _handleToolbarAction: function(action, $container) {
            switch (action) {
                case 'copy':
                    this._copyToClipboard($container);
                    break;
                case 'expand-all':
                    $container.find('.json-collapsible').removeClass('collapsed');
                    break;
                case 'collapse-all':
                    $container.find('.json-collapsible').addClass('collapsed');
                    break;
            }
        },

        /**
         * 复制到剪贴板
         */
        _copyToClipboard: function($container) {
            var text = $container.find('.json-code-content').text();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    this._showCopySuccess($container);
                }.bind(this));
            } else {
                // 降级方案
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this._showCopySuccess($container);
            }
        },

        /**
         * 显示复制成功提示
         */
        _showCopySuccess: function($container) {
            var $success = $('<div class="json-copy-success">复制成功!</div>');
            $container.append($success);
            setTimeout(function() {
                $success.remove();
            }, 2000);
        },

        /**
         * 执行搜索
         */
        _performSearch: function(searchTerm, $container) {
            $container.find('.json-search-result').removeClass('json-search-result json-search-current');
            
            if (searchTerm) {
                var regex = new RegExp(this._escapeRegex(searchTerm), 'gi');
                $container.find('.json-code-content').find('*').each(function() {
                    var $this = $(this);
                    var text = $this.text();
                    if (regex.test(text)) {
                        $this.addClass('json-search-result');
                    }
                });
            }
        },

        /**
         * 显示错误
         */
        _showError: function($container, message) {
            var html = '<div class="json-error">';
            html += '<div class="json-error-title">JSON解析错误</div>';
            html += '<div class="json-error-message">' + this._escapeHtml(message) + '</div>';
            html += '</div>';
            $container.html(html);
        },

        /**
         * HTML转义
         */
        _escapeHtml: function(text) {
            var div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        /**
         * 正则表达式转义
         */
        _escapeRegex: function(text) {
            return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    };

    // 全局实例
    window.JsonFormatter = JsonFormatter;
    
    // jQuery插件
    if ($) {
        $.fn.jsonFormatter = function(method, data, options) {
            var formatter = new JsonFormatter();
            
            if (method === 'compare') {
                return formatter.compare(data, arguments[2], this, options);
            } else {
                return formatter.format(data, this, options);
            }
        };
    }

})(window, window.jQuery);
