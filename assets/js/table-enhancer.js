/**
 * 表格增强模块
 * 提供表格排序、搜索、分页和固定表头等功能
 */

(function(window, $) {
    'use strict';

    // 表格增强器类
    function TableEnhancer() {
        this.options = {
            sortable: true,
            searchable: true,
            pageable: true,
            pageSize: 20,
            stickyHeader: true,
            virtualScroll: false,
            showPageInfo: true,
            searchPlaceholder: '搜索...',
            noDataText: '暂无数据',
            loadingText: '加载中...'
        };
        this.currentPage = 1;
        this.totalRows = 0;
        this.filteredRows = 0;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchTerm = '';
    }

    TableEnhancer.prototype = {
        /**
         * 初始化表格增强功能
         * @param {string|jQuery} tableSelector - 表格选择器
         * @param {object} options - 配置选项
         */
        init: function(tableSelector, options) {
            this.$table = $(tableSelector);
            this.options = $.extend({}, this.options, options);
            
            if (this.$table.length === 0) {
                console.warn('Table not found:', tableSelector);
                return;
            }

            this._setupTable();
            this._bindEvents();
            this._updateDisplay();
            
            return this;
        },

        /**
         * 设置表格结构
         */
        _setupTable: function() {
            var self = this;
            
            // 包装表格
            if (!this.$table.parent().hasClass('table-enhancer-wrapper')) {
                this.$table.wrap('<div class="table-enhancer-wrapper"></div>');
            }
            this.$wrapper = this.$table.parent();
            
            // 添加控制面板
            this._createControlPanel();
            
            // 设置固定表头
            if (this.options.stickyHeader) {
                this._setupStickyHeader();
            }
            
            // 设置排序
            if (this.options.sortable) {
                this._setupSorting();
            }
            
            // 初始化数据
            this._cacheTableData();
        },

        /**
         * 创建控制面板
         */
        _createControlPanel: function() {
            var html = '<div class="table-enhancer-controls">';
            
            if (this.options.searchable) {
                html += '<div class="table-search-container">';
                html += '<input type="text" class="table-search-input" placeholder="' + this.options.searchPlaceholder + '">';
                html += '<span class="table-search-icon">🔍</span>';
                html += '</div>';
            }
            
            html += '<div class="table-info-container">';
            html += '<span class="table-info"></span>';
            html += '</div>';
            
            html += '</div>';
            
            this.$wrapper.prepend(html);
            this.$controls = this.$wrapper.find('.table-enhancer-controls');
        },

        /**
         * 设置固定表头
         */
        _setupStickyHeader: function() {
            this.$table.addClass('table-sticky-header');
        },

        /**
         * 设置排序功能
         */
        _setupSorting: function() {
            var self = this;
            
            this.$table.find('thead th').each(function(index) {
                var $th = $(this);
                if (!$th.hasClass('no-sort')) {
                    $th.addClass('sortable');
                    $th.append('<span class="sort-indicator"></span>');
                }
            });
        },

        /**
         * 缓存表格数据
         */
        _cacheTableData: function() {
            var self = this;
            this.originalRows = [];
            
            this.$table.find('tbody tr').each(function() {
                var $row = $(this);
                var rowData = {
                    element: $row,
                    text: $row.text().toLowerCase(),
                    cells: []
                };
                
                $row.find('td').each(function() {
                    rowData.cells.push($(this).text().trim());
                });
                
                self.originalRows.push(rowData);
            });
            
            this.totalRows = this.originalRows.length;
            this.filteredRows = this.totalRows;
        },

        /**
         * 绑定事件
         */
        _bindEvents: function() {
            var self = this;
            
            // 搜索事件
            if (this.options.searchable) {
                this.$wrapper.on('input', '.table-search-input', function() {
                    self.searchTerm = $(this).val().toLowerCase();
                    self._performSearch();
                });
            }
            
            // 排序事件
            if (this.options.sortable) {
                this.$table.on('click', 'thead th.sortable', function() {
                    var columnIndex = $(this).index();
                    self._performSort(columnIndex);
                });
            }
            
            // 分页事件
            if (this.options.pageable) {
                this.$wrapper.on('click', '.pagination-btn', function() {
                    var action = $(this).data('action');
                    self._handlePagination(action);
                });
            }
        },

        /**
         * 执行搜索
         */
        _performSearch: function() {
            var self = this;
            var filteredRows = [];
            
            if (this.searchTerm === '') {
                filteredRows = this.originalRows;
            } else {
                filteredRows = this.originalRows.filter(function(row) {
                    return row.text.indexOf(self.searchTerm) !== -1;
                });
            }
            
            this.filteredRows = filteredRows.length;
            this.currentPage = 1;
            this._displayRows(filteredRows);
            this._updateDisplay();
        },

        /**
         * 执行排序
         */
        _performSort: function(columnIndex) {
            var self = this;
            
            // 更新排序状态
            if (this.sortColumn === columnIndex) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortColumn = columnIndex;
                this.sortDirection = 'asc';
            }
            
            // 更新排序指示器
            this.$table.find('thead th .sort-indicator').removeClass('asc desc');
            this.$table.find('thead th').eq(columnIndex).find('.sort-indicator')
                .addClass(this.sortDirection);
            
            // 排序数据
            var sortedRows = this.originalRows.slice().sort(function(a, b) {
                var aVal = a.cells[columnIndex] || '';
                var bVal = b.cells[columnIndex] || '';
                
                // 尝试数字排序
                var aNum = parseFloat(aVal);
                var bNum = parseFloat(bVal);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return self.sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                // 字符串排序
                if (self.sortDirection === 'asc') {
                    return aVal.localeCompare(bVal);
                } else {
                    return bVal.localeCompare(aVal);
                }
            });
            
            // 如果有搜索条件，重新过滤
            if (this.searchTerm) {
                sortedRows = sortedRows.filter(function(row) {
                    return row.text.indexOf(self.searchTerm) !== -1;
                });
            }
            
            this.currentPage = 1;
            this._displayRows(sortedRows);
            this._updateDisplay();
        },

        /**
         * 显示行数据
         */
        _displayRows: function(rows) {
            var self = this;
            var $tbody = this.$table.find('tbody');
            
            // 清空现有行
            $tbody.empty();
            
            if (rows.length === 0) {
                $tbody.append('<tr><td colspan="100%" class="text-center">' + this.options.noDataText + '</td></tr>');
                return;
            }
            
            // 分页处理
            var startIndex = 0;
            var endIndex = rows.length;
            
            if (this.options.pageable) {
                startIndex = (this.currentPage - 1) * this.options.pageSize;
                endIndex = Math.min(startIndex + this.options.pageSize, rows.length);
            }
            
            // 显示当前页的行
            for (var i = startIndex; i < endIndex; i++) {
                $tbody.append(rows[i].element.clone());
            }
            
            // 更新分页控件
            if (this.options.pageable) {
                this._updatePagination(rows.length);
            }
        },

        /**
         * 更新分页控件
         */
        _updatePagination: function(totalRows) {
            var totalPages = Math.ceil(totalRows / this.options.pageSize);
            
            // 移除现有分页控件
            this.$wrapper.find('.table-pagination').remove();
            
            if (totalPages <= 1) return;
            
            var html = '<div class="table-pagination">';
            
            // 上一页
            html += '<button class="pagination-btn" data-action="prev"' + 
                   (this.currentPage === 1 ? ' disabled' : '') + '>上一页</button>';
            
            // 页码
            var startPage = Math.max(1, this.currentPage - 2);
            var endPage = Math.min(totalPages, this.currentPage + 2);
            
            if (startPage > 1) {
                html += '<button class="pagination-btn" data-action="page" data-page="1">1</button>';
                if (startPage > 2) {
                    html += '<span class="pagination-ellipsis">...</span>';
                }
            }
            
            for (var i = startPage; i <= endPage; i++) {
                html += '<button class="pagination-btn' + (i === this.currentPage ? ' active' : '') + 
                       '" data-action="page" data-page="' + i + '">' + i + '</button>';
            }
            
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += '<span class="pagination-ellipsis">...</span>';
                }
                html += '<button class="pagination-btn" data-action="page" data-page="' + totalPages + '">' + totalPages + '</button>';
            }
            
            // 下一页
            html += '<button class="pagination-btn" data-action="next"' + 
                   (this.currentPage === totalPages ? ' disabled' : '') + '>下一页</button>';
            
            html += '</div>';
            
            this.$wrapper.append(html);
        },

        /**
         * 处理分页操作
         */
        _handlePagination: function(action) {
            var totalPages = Math.ceil(this.filteredRows / this.options.pageSize);
            
            switch (action) {
                case 'prev':
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                    break;
                case 'next':
                    if (this.currentPage < totalPages) {
                        this.currentPage++;
                    }
                    break;
                case 'page':
                    var page = parseInt($(event.target).data('page'));
                    if (page >= 1 && page <= totalPages) {
                        this.currentPage = page;
                    }
                    break;
            }
            
            this._performSearch(); // 重新应用当前的搜索和排序
        },

        /**
         * 更新显示信息
         */
        _updateDisplay: function() {
            if (!this.options.showPageInfo) return;
            
            var info = '';
            if (this.filteredRows === 0) {
                info = '暂无数据';
            } else {
                var start = (this.currentPage - 1) * this.options.pageSize + 1;
                var end = Math.min(this.currentPage * this.options.pageSize, this.filteredRows);
                info = '显示 ' + start + '-' + end + ' 条，共 ' + this.filteredRows + ' 条';
                
                if (this.searchTerm && this.filteredRows !== this.totalRows) {
                    info += ' (从 ' + this.totalRows + ' 条中筛选)';
                }
            }
            
            this.$wrapper.find('.table-info').text(info);
        },

        /**
         * 刷新表格数据
         */
        refresh: function() {
            this._cacheTableData();
            this.currentPage = 1;
            this.searchTerm = '';
            this.$wrapper.find('.table-search-input').val('');
            this._performSearch();
        },

        /**
         * 销毁增强功能
         */
        destroy: function() {
            this.$wrapper.find('.table-enhancer-controls').remove();
            this.$wrapper.find('.table-pagination').remove();
            this.$table.removeClass('table-sticky-header');
            this.$table.find('thead th').removeClass('sortable').find('.sort-indicator').remove();
            this.$table.unwrap();
        }
    };

    // 全局实例
    window.TableEnhancer = TableEnhancer;
    
    // jQuery插件
    if ($) {
        $.fn.tableEnhancer = function(options) {
            return this.each(function() {
                var enhancer = new TableEnhancer();
                enhancer.init(this, options);
                $(this).data('tableEnhancer', enhancer);
            });
        };
    }

})(window, window.jQuery);
