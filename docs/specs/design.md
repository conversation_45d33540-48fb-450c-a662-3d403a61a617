# JSON Diff可视化展示优化 - Design Document

## Overview

### 设计目标
基于现有的`GetDiffResult`方法，优化diff结果的HTML展示效果，提供更直观的JSON差异可视化。设计重点在于：
- **保持兼容性**：不改变现有接口和流程
- **渐进式增强**：在现有基础上增加可视化功能
- **用户体验**：提供清晰的差异标识和交互功能

### 设计原则
1. **最小化改动**：只修改`GenerateArrayHTMLFromString`函数的HTML生成逻辑
2. **性能优先**：确保大型JSON的渲染性能
3. **移动端适配**：响应式设计支持各种设备
4. **安全性**：防止XSS攻击和内存泄漏

## Architecture

### 整体架构
```
现有流程保持不变：
GetDiffResult → Compare → GenerateArrayHTMLFromString → HTML文件 → COS上传

优化点：
GenerateArrayHTMLFromString函数增强，生成更丰富的HTML内容
```

### 核心组件设计

#### 1. 增强的HTML生成器
- **位置**：`service/tools/diffJob/diffRegister/util.go`
- **函数**：`GenerateArrayHTMLFromString`
- **功能**：生成包含CSS和JavaScript的完整HTML页面

#### 2. 差异解析器
- **位置**：`service/tools/diffJob/diffRegister/util.go`
- **新增函数**：`parseDiffItems`
- **功能**：解析diff数组，提取差异类型和内容

#### 3. 模板系统
- **内嵌模板**：在Go代码中定义HTML模板
- **样式模板**：包含CSS样式定义
- **交互模板**：包含JavaScript交互逻辑

## Components and Interfaces

### 核心函数接口

#### GenerateArrayHTMLFromString（增强版）
```go
func GenerateArrayHTMLFromString(ctx *gin.Context, arr []string, id int64) (path string, err error)
```
**输入**：
- `arr []string`：差异字符串数组
- `id int64`：记录ID，用于文件命名

**输出**：
- `path string`：生成的HTML文件在COS中的路径
- `error`：错误信息

**增强功能**：
- 解析差异字符串，识别差异类型
- 生成带颜色编码的HTML
- 添加交互式折叠/展开功能
- 包含差异统计信息

#### 新增辅助函数

##### parseDiffItems
```go
func parseDiffItems(arr []string) ([]DiffItem, error)
```
**功能**：解析差异字符串数组，结构化差异信息

**返回**：
```go
type DiffItem struct {
    Type     string      // "create", "update", "delete"
    Path     []string    // JSON路径
    From     interface{} // 原值
    To       interface{} // 新值
    Raw      string      // 原始字符串
}
```

##### generateEnhancedHTML
```go
func generateEnhancedHTML(items []DiffItem, id int64) (string, error)
```
**功能**：生成完整的HTML页面内容

### HTML模板结构

#### HTML页面结构
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSON Diff可视化</title>
    <style>/* 内嵌CSS样式 */</style>
</head>
<body>
    <div class="diff-container">
        <div class="diff-header">差异统计信息</div>
        <div class="diff-content">差异详情内容</div>
    </div>
    <script>/* 内嵌JavaScript交互逻辑 */</script>
</body>
</html>
```

#### 差异展示组件
```html
<div class="diff-item" data-type="create|update|delete">
    <div class="diff-path">JSON路径</div>
    <div class="diff-change">
        <div class="diff-from">原值</div>
        <div class="diff-to">新值</div>
    </div>
</div>
```

## Data Models

### DiffItem结构
```go
type DiffItem struct {
    Type     string      `json:"type"`     // 差异类型：create/update/delete
    Path     []string    `json:"path"`     // JSON路径，如 ["user", "name"]
    From     interface{} `json:"from"`     // 原值
    To       interface{} `json:"to"`       // 新值
    Raw      string      `json:"raw"`      // 原始字符串
}
```

### DiffStats结构
```go
type DiffStats struct {
    Total    int `json:"total"`    // 总差异数量
    Create   int `json:"create"`   // 新增数量
    Update   int `json:"update"`   // 修改数量
    Delete   int `json:"delete"`   // 删除数量
}
```

### 颜色映射配置
```go
var DiffColors = map[string]string{
    "create": "#90EE90", // 浅绿色
    "update": "#FED8B1", // 浅橙色  
    "delete": "#FFB6C1", // 浅红色
}
```

## Error Handling

### 错误处理策略

#### 1. 解析错误处理
- **场景**：差异字符串格式错误
- **处理**：记录错误日志，降级为原始文本显示
- **日志**：`zlog.Warnf(ctx, "Failed to parse diff item: %v", err)`

#### 2. HTML生成错误处理
- **场景**：模板渲染失败
- **处理**：使用简化模板，确保基本功能可用
- **回退**：降级到现有的简单HTML生成

#### 3. 文件操作错误处理
- **场景**：临时文件创建或删除失败
- **处理**：重试机制，失败后返回错误
- **清理**：确保临时文件最终被删除

#### 4. 内存使用控制
- **场景**：大型JSON处理
- **处理**：分批处理，限制内存使用
- **监控**：添加内存使用监控

### 错误类型定义
```go
type DiffError struct {
    Code    string
    Message string
    Cause   error
}

func (e *DiffError) Error() string {
    return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}
```

## Testing Strategy

### 测试策略

#### 1. 单元测试
**目标**：测试核心函数的正确性
**测试函数**：
- `TestParseDiffItems`：测试差异解析功能
- `TestGenerateEnhancedHTML`：测试HTML生成功能
- `TestDiffItemValidation`：测试差异项验证

#### 2. 集成测试
**目标**：测试整个流程的端到端功能
**测试场景**：
- 完整的diff生成流程
- HTML文件上传和访问
- 不同类型差异的展示效果

#### 3. 性能测试
**目标**：确保性能要求达标
**测试指标**：
- 大型JSON处理时间 < 3秒
- 内存使用量监控
- 并发处理能力

#### 4. 兼容性测试
**目标**：确保向后兼容性
**测试内容**：
- 现有接口调用不受影响
- 生成的HTML在不同浏览器中正常显示
- 移动端适配测试

### 测试数据设计

#### 测试用例1：简单对象差异
```json
// 原数据
{"name": "张三", "age": 20}
// 新数据  
{"name": "李四", "age": 21, "gender": "男"}
```

#### 测试用例2：复杂嵌套差异
```json
// 包含数组、嵌套对象的复杂JSON差异
```

#### 测试用例3：大型数据集
```json
// 包含1000+条记录的大型数组差异
```

### 测试自动化

#### CI/CD集成
- 在现有CI流程中添加diff相关测试
- 自动化性能测试和监控
- 代码覆盖率要求（>80%）

#### 性能监控
- 添加性能指标收集
- 设置性能告警阈值
- 定期性能回归测试
