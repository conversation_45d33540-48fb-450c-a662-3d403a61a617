# Diff可视化展示优化 - Design Document

## Overview

本设计旨在优化现有的GetDiffResult方法，提供更直观的JSON数据差异可视化展示。通过引入现代化的前端组件和改进的HTML生成逻辑，实现并排对比、交互式探索和性能优化。

### 设计原则
1. **渐进式增强**：在现有功能基础上增强，不破坏现有功能
2. **性能优先**：大数据量下保持良好的响应速度
3. **用户体验**：直观的视觉反馈和流畅的交互
4. **可维护性**：清晰的代码结构和组件化设计

## Architecture

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Storage       │
│   (Browser)     │◄──►│   (Go Server)   │◄──►│   (COS)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Enhanced UI    │    │  Diff Engine    │    │  HTML Files     │
│  - React/Vue    │    │  - JSON Diff    │    │  - Static       │
│  - Comparison   │    │  - HTML Gen     │    │  - Cached       │
│  - Interactive  │    │  - Config       │    │  - Versioned    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件
1. **DiffEngine**：处理JSON差异计算的核心逻辑
2. **HTMLGenerator**：生成增强的可视化HTML页面
3. **FrontendComponents**：提供交互式用户界面
4. **ConfigManager**：管理差异比较的配置

## Components and Interfaces

### 1. EnhancedDiffGenerator（增强的差异生成器）
```go
type EnhancedDiffGenerator struct {
    config      DiffConfig
    templateMgr *TemplateManager
    stats       *DiffStats
}

func (g *EnhancedDiffGenerator) GenerateEnhancedHTML(
    ctx *gin.Context, 
    diffs []Diff, 
    oldData, newData interface{},
    id int64
) (string, error)
```

### 2. InteractiveHTMLTemplate（交互式HTML模板）
```go
type InteractiveHTMLTemplate struct {
    SideBySide    bool   // 是否启用并排对比
    ShowStats     bool   // 是否显示统计信息
    Searchable    bool   // 是否支持搜索
    Collapsible   bool   // 是否支持折叠
    Theme         string // 主题样式
}
```

### 3. DiffStats（差异统计）
```go
type DiffStats struct {
    TotalCount    int64            `json:"totalCount"`
    CreateCount   int64            `json:"createCount"`
    UpdateCount   int64            `json:"updateCount"`
    DeleteCount   int64            `json:"deleteCount"`
    PathStats     map[string]int64 `json:"pathStats"`    // 各路径的差异数量
    SizeStats     SizeStats        `json:"sizeStats"`     // 大小统计
}
```

### 4. Frontend JavaScript API
```javascript
class DiffViewer {
    constructor(config) {
        this.config = config;
        this.init();
    }
    
    // 渲染差异
    renderDiffs(diffs);
    
    // 搜索功能
    search(keyword);
    
    // 筛选功能
    filterByType(type);
    
    // 展开/折叠
    toggleNode(path);
    
    // 导出功能
    export(format);
}
```

## Data Models

### 1. EnhancedDiff（增强的差异结构）
```go
type EnhancedDiff struct {
    Path      string      `json:"path"`
    Type      DiffType    `json:"type"`
    OldValue  interface{} `json:"oldValue"`
    NewValue  interface{} `json:"newValue"`
    Context   interface{} `json:"context"`   // 上下文信息
    Metadata  DiffMeta    `json:"metadata"`  // 元数据
}

type DiffMeta struct {
    ChangeSize    int64   `json:"changeSize"`    // 变化大小
    Importance    int     `json:"importance"`    // 重要性等级
    Category      string  `json:"category"`      // 分类
    Tags          []string `json:"tags"`         // 标签
}
```

### 2. ComparisonResult（比较结果）
```go
type ComparisonResult struct {
    ID          int64           `json:"id"`
    Timestamp   time.Time       `json:"timestamp"`
    OldData     interface{}     `json:"oldData"`
    NewData     interface{}     `json:"newData"`
    Diffs       []EnhancedDiff  `json:"diffs"`
    Stats       DiffStats       `json:"stats"`
    Config      DiffConfig      `json:"config"`
}
```

### 3. ViewConfig（视图配置）
```go
type ViewConfig struct {
    DisplayMode   string   `json:"displayMode"`   // unified/split
    ShowContext   bool     `json:"showContext"`   // 是否显示上下文
    ContextLines  int      `json:"contextLines"`  // 上下文行数
    WordDiff      bool     `json:"wordDiff"`      // 是否启用词级差异
    IgnoreWhitespace bool   `json:"ignoreWhitespace"` // 是否忽略空白字符
    CollapseThreshold int   `json:"collapseThreshold"` // 折叠阈值
    HighlightRules []HighlightRule `json:"highlightRules"` // 高亮规则
}
```

## Error Handling

### 1. 错误分类
- **ParseError**：JSON解析错误
- **DiffError**：差异计算错误
- **GenerationError**：HTML生成错误
- **StorageError**：文件存储错误
- **NetworkError**：网络请求错误

### 2. 错误处理策略
```go
type DiffErrorHandler struct {
    logger *zlog.Logger
    metrics *MetricsCollector
}

func (h *DiffErrorHandler) HandleError(ctx *gin.Context, err error) {
    switch e := err.(type) {
    case *ParseError:
        h.handleParseError(ctx, e)
    case *DiffError:
        h.handleDiffError(ctx, e)
    case *GenerationError:
        h.handleGenerationError(ctx, e)
    default:
        h.handleGenericError(ctx, err)
    }
}
```

### 3. 用户友好的错误提示
- JSON格式错误：显示具体的位置和原因
- 大小超限：提供压缩或分块处理建议
- 超时错误：显示进度和重试选项
- 权限错误：引导用户获取必要权限

## Testing Strategy

### 1. 单元测试
```go
func TestEnhancedDiffGenerator_GenerateHTML(t *testing.T) {
    tests := []struct {
        name     string
        oldData  string
        newData  string
        expected string
    }{
        {
            name:    "simple object diff",
            oldData: `{"name":"test","value":1}`,
            newData: `{"name":"test","value":2}`,
        },
        // 更多测试用例...
    }
}
```

### 2. 集成测试
- 端到端的diff流程测试
- 前端交互功能测试
- 性能压力测试
- 兼容性测试

### 3. 性能测试
```go
func BenchmarkDiffGeneration(b *testing.B) {
    oldData := generateLargeJSON(10 * 1024 * 1024) // 10MB
    newData := modifyJSON(oldData)
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := GenerateEnhancedHTML(oldData, newData)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

### 4. 用户验收测试
- 可视化效果验证
- 交互功能测试
- 响应式布局测试
- 跨浏览器兼容性测试
