{{ define "desk/datadiff/diffdetail.html"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据差异对比详情 - 流量diff工具</title>

    <!-- CSS Dependencies -->
    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?v={{.timestamp}}"/>
    <link rel="stylesheet" href="/fwyytool/assets/css/datadiff-enhanced.css?v={{.timestamp}}"/>
    <link rel="stylesheet" href="/fwyytool/assets/css/json-viewer.css?v={{.timestamp}}"/>
</head>
<body>
    <div class="datadiff-container">
        <!-- 页面标题 -->
        <header class="datadiff-header">
            <h3>数据差异对比详情</h3>
            <p>API接口新老版本数据差异对比工具</p>
        </header>

        <!-- 昨日小计区域 -->
        <section class="yesterday-summary">
            <a href="/fwyytool/arkgo/tool/getdiffcount" class="yesterday-summary-link" onclick="toggleData(event)">
                📊 昨日小计
            </a>
            <div id="data" class="yesterday-data"></div>
        </section>

        <!-- 查询表单 -->
        <section class="datadiff-form">
            <form class="form-inline" method="GET">
                <h4 class="form-title">查询条件</h4>

                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="timeRange">时间范围</label>
                        <select class="form-control" id="timeRange" name="timeRange">
                            <option value="1" {{if eq .params.TimeRange 1}}selected{{end}}>一天内的数据</option>
                            <option value="2" {{if eq .params.TimeRange 2}}selected{{end}}>两天内的数据</option>
                            <option value="3" {{if eq .params.TimeRange 3}}selected{{end}}>三天内的数据</option>
                            <option value="4" {{if eq .params.TimeRange 4}}selected{{end}}>四天内的数据</option>
                            <option value="5" {{if eq .params.TimeRange 5}}selected{{end}}>五天内的数据</option>
                        </select>
                    </div>

                    <div class="form-group col-md-6">
                        <label for="handler">接口名称</label>
                        <select class="form-control" id="handler" name="handler">
                            <option value="">请选择接口</option>
                            <option value="StudentCallInfo_0" {{if eq .params.Handler "StudentCallInfo_0"}}selected{{end}}>StudentCallInfo_0</option>
                            <option value="UserInfo_0" {{if eq .params.Handler "UserInfo_0"}}selected{{end}}>UserInfo_0</option>
                            <option value="AllowAutoCallAndMessage_0" {{if eq .params.Handler "AllowAutoCallAndMessage_0"}}selected{{end}}>AllowAutoCallAndMessage_0</option>
                            <option value="GetOptions_0" {{if eq .params.Handler "GetOptions_0"}}selected{{end}}>GetOptions_0</option>
                            <option value="GetCustomTag_0" {{if eq .params.Handler "GetCustomTag_0"}}selected{{end}}>GetCustomTag_0</option>
                            <option value="GetWxBindInfo_0" {{if eq .params.Handler "GetWxBindInfo_0"}}selected{{end}}>GetWxBindInfo_0</option>
                            <option value="InterviewRecordV2_0" {{if eq .params.Handler "InterviewRecordV2_0"}}selected{{end}}>InterviewRecordV2_0</option>
                            <option value="InterviewReferLpc_0" {{if eq .params.Handler "InterviewReferLpc_0"}}selected{{end}}>InterviewReferLpc_0</option>
                            <option value="GetSchemaByCourseId_0" {{if eq .params.Handler "GetSchemaByCourseId_0"}}selected{{end}}>GetSchemaByCourseId_0</option>
                            <option value="GetSipInfo_0" {{if eq .params.Handler "GetSipInfo_0"}}selected{{end}}>GetSipInfo_0</option>
                            <option value="GetStudentCallRecordInfo_0" {{if eq .params.Handler "GetStudentCallRecordInfo_0"}}selected{{end}}>GetStudentCallRecordInfo_0</option>
                            <option value="CourseRecordDefaultOption_0" {{if eq .params.Handler "CourseRecordDefaultOption_0"}}selected{{end}}>CourseRecordDefaultOption_0</option>
                            <option value="CourseRecordMeta_0" {{if eq .params.Handler "CourseRecordMeta_0"}}selected{{end}}>CourseRecordMeta_0</option>
                            <option value="CourseRecordV2_0" {{if eq .params.Handler "CourseRecordV2_0"}}selected{{end}}>CourseRecordV2_0</option>
                            <option value="DetailConfig_0" {{if eq .params.Handler "DetailConfig_0"}}selected{{end}}>DetailConfig_0</option>
                            <option value="GetCourseTimeTableDay_0" {{if eq .params.Handler "GetCourseTimeTableDay_0"}}selected{{end}}>GetCourseTimeTableDay_0</option>
                            <option value="GetCourseTimeTableWeek_0" {{if eq .params.Handler "GetCourseTimeTableWeek_0"}}selected{{end}}>GetCourseTimeTableWeek_0</option>
                            <option value="GetStudentOrderList_0" {{if eq .params.Handler "GetStudentOrderList_0"}}selected{{end}}>GetStudentOrderList_0</option>
                            <option value="KeyBehavior_0" {{if eq .params.Handler "KeyBehavior_0"}}selected{{end}}>KeyBehavior_0</option>
                            <option value="PerformanceV1_0" {{if eq .params.Handler "PerformanceV1_0"}}selected{{end}}>PerformanceV1_0</option>
                            <option value="StudentDelaminationDiffrenceListV1_0" {{if eq .params.Handler "StudentDelaminationDiffrenceListV1_0"}}selected{{end}}>StudentDelaminationDiffrenceListV1_0</option>
                            <option value="StudentDetailV1_0" {{if eq .params.Handler "StudentDetailV1_0"}}selected{{end}}>StudentDetailV1_0</option>
                            <option value="GetStudentBind_0" {{if eq .params.Handler "GetStudentBind_0"}}selected{{end}}>GetStudentBind_0</option>
                            <option value="GetActiveWithBindData_0" {{if eq .params.Handler "GetActiveWithBindData_0"}}selected{{end}}>GetActiveWithBindData_0</option>
                        </select>
                        <small class="form-text text-muted">后缀0:新老diff，后缀1:环比diff</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                        <i class="icon-search"></i> 查询数据
                    </button>

                    {{if .errMsg}}
                    <div class="error-container enhanced-error">
                        <div class="error-header">
                            <span class="error-icon">⚠️</span>
                            <span class="error-title">{{.errMsg}}</span>
                        </div>

                        {{if .errorDetails}}
                        <div class="error-details">
                            <strong>详细信息:</strong> {{.errorDetails}}
                        </div>
                        {{end}}

                        {{if .suggestions}}
                        <div class="error-suggestions">
                            <strong>建议解决方案:</strong>
                            <ul>
                                {{range .suggestions}}
                                <li>{{.}}</li>
                                {{end}}
                            </ul>
                        </div>
                        {{end}}

                        {{if .requestTime}}
                        <div class="error-meta">
                            <small>请求耗时: {{.requestTime}}ms | 时间: {{.timestamp}}</small>
                        </div>
                        {{end}}
                    </div>
                    {{end}}
                </div>
            </form>
        </section>
        <!-- 统计数据卡片 -->
        <section class="stats-container">
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>总任务数</th>
                        <th>有差异</th>
                        <th>无差异</th>
                        <th>未完成</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="stat-total">{{.data.Total}}</td>
                        <td class="stat-diff">{{.data.HasDiffNum}}</td>
                        <td class="stat-no-diff">{{.data.NoDiffNum}}</td>
                        <td class="stat-unfinished">{{.data.UnFinishedTask}}</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- 结果展示标签页 -->
        <section class="datadiff-tabs">
            <ul class="nav nav-tabs" id="tabService">
                <li role="presentation" class="active">
                    <a class="chapterTaskList">
                        📋 差异详情记录 (前100条有差异的记录)
                    </a>
                </li>
            </ul>
        </section>

        <!-- 数据表格区域 -->
        <section class="datadiff-table-container">
            <table class="datadiff-table" id="diffDataTable">
                <thead>
                    <tr>
                        <th class="sortable" data-column="params">请求参数</th>
                        <th class="sortable" data-column="diffNum">差异数量</th>
                        <th class="sortable" data-column="oldData">旧版本数据</th>
                        <th class="sortable" data-column="newData">新版本数据</th>
                        <th class="no-sort">差异详情</th>
                        <th class="sortable" data-column="handlerName">接口名称</th>
                        <th class="sortable" data-column="updateTime">更新时间</th>
                        <th class="sortable" data-column="createTime">创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    {{range $key, $diffInfo := .data.DataDiffList}}
                    <tr data-diff-num="{{$diffInfo.DiffNum}}" data-handler="{{$diffInfo.HandlerName}}">
                        <td>
                            <div class="json-cell">
                                <textarea class="json-textarea" readonly>{{jsonPrettyStr $diffInfo.Params}}</textarea>
                            </div>
                        </td>
                        <td class="diff-count {{if gt $diffInfo.DiffNum 0}}has-diff{{end}}">
                            {{$diffInfo.DiffNum}}
                        </td>
                        <td>
                            <div class="json-cell">
                                <textarea class="json-textarea" readonly>{{jsonPrettyStr $diffInfo.OldData}}</textarea>
                            </div>
                        </td>
                        <td>
                            <div class="json-cell">
                                <textarea class="json-textarea" readonly>{{jsonPrettyStr $diffInfo.NewData}}</textarea>
                            </div>
                        </td>
                        <td class="diff-link-cell">
                            <a href="{{$diffInfo.DiffResult}}" class="diff-link" target="_blank">
                                🔍 查看差异
                            </a>
                        </td>
                        <td class="handler-name">{{$diffInfo.HandlerName}}</td>
                        <td class="time-cell">{{showTime $diffInfo.UpdateTime}}</td>
                        <td class="time-cell">{{showTime $diffInfo.CreateTime}}</td>
                    </tr>
                    {{end}}
                </tbody>
            </table>
        </section>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <script src="/fwyytool/assets/js/json-formatter.js?v={{.timestamp}}"></script>
    <script src="/fwyytool/assets/js/table-enhancer.js?v={{.timestamp}}"></script>
    <script src="/fwyytool/assets/js/datadiff-enhanced.js?v={{.timestamp}}"></script>

    <script>
        // 全局变量
        let dataLoaded = false;

        // 页面初始化
        $(document).ready(function() {
            // 确保表格增强器已加载后再初始化
            if (typeof $.fn.tableEnhancer === 'function') {
                // 初始化表格增强功能
                $('#diffDataTable').tableEnhancer({
                    sortable: true,
                    searchable: true,
                    pageable: true,
                    pageSize: 20,
                    stickyHeader: true,
                    searchPlaceholder: '搜索接口名、参数或数据...',
                    noDataText: '暂无差异数据'
                });
            } else {
                console.warn('TableEnhancer plugin not loaded, falling back to basic functionality');
                // 提供基本的搜索功能作为降级方案
                initBasicSearch();
            }

            // 初始化JSON格式化和增强功能
            $('.json-textarea').each(function() {
                const $textarea = $(this);
                const content = $textarea.val();

                if (content && content.trim()) {
                    try {
                        const parsed = JSON.parse(content);
                        const formatted = JSON.stringify(parsed, null, 2);
                        $textarea.val(formatted);

                        // 添加JSON查看器功能
                        $textarea.addClass('enhanced-json');
                    } catch (e) {
                        // 保持原始内容，但标记为非JSON
                        $textarea.addClass('non-json');
                    }
                }
            });

            // 添加差异高亮功能
            enhanceDiffDisplay();

            // 添加键盘快捷键
            setupKeyboardShortcuts();

            // 添加工具提示
            setupTooltips();

            // 优化表单提交
            enhanceFormSubmission();
        });

        // 增强差异显示
        function enhanceDiffDisplay() {
            $('tr[data-diff-num]').each(function() {
                const $row = $(this);
                const diffNum = parseInt($row.data('diff-num'));

                if (diffNum > 0) {
                    $row.addClass('has-differences');

                    // 添加差异指示器
                    $row.find('.diff-count').prepend('<span class="diff-indicator">⚠️</span>');
                }
            });
        }

        // 设置键盘快捷键
        function setupKeyboardShortcuts() {
            $(document).on('keydown', function(e) {
                // Ctrl/Cmd + F: 聚焦搜索框
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    $('.table-search-input').focus();
                }

                // Esc: 清空搜索
                if (e.key === 'Escape') {
                    $('.table-search-input').val('').trigger('input');
                }
            });
        }

        // 设置工具提示
        function setupTooltips() {
            // 为差异链接添加提示
            $('.diff-link').attr('title', '点击查看详细的差异对比结果');

            // 为排序列添加提示
            $('.sortable').attr('title', '点击进行排序');

            // 为JSON文本域添加提示
            $('.json-textarea').attr('title', '双击可展开查看完整内容');
        }

        // 增强表单提交
        function enhanceFormSubmission() {
            $('form.form-inline').on('submit', function() {
                const $form = $(this);
                const $submitBtn = $form.find('button[type="submit"]');

                // 显示提交状态
                $submitBtn.prop('disabled', true).html('<i class="loading-icon">⏳</i> 查询中...');

                // 添加加载动画
                $('body').addClass('loading');
            });
        }

        // 基本搜索功能（降级方案）
        function initBasicSearch() {
            // 创建搜索框
            var searchHtml = '<div class="basic-search-container" style="margin-bottom: 20px;">' +
                            '<input type="text" id="basicSearchInput" placeholder="搜索接口名、参数或数据..." ' +
                            'style="width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">' +
                            '</div>';
            $('#diffDataTable').before(searchHtml);

            // 绑定搜索事件
            $('#basicSearchInput').on('input', function() {
                var searchTerm = $(this).val().toLowerCase();
                var visibleCount = 0;

                $('#diffDataTable tbody tr').each(function() {
                    var rowText = $(this).text().toLowerCase();
                    if (searchTerm === '' || rowText.indexOf(searchTerm) !== -1) {
                        $(this).show();
                        visibleCount++;
                    } else {
                        $(this).hide();
                    }
                });

                // 显示搜索结果统计
                updateSearchStats(visibleCount, $('#diffDataTable tbody tr').length);
            });

            // 添加搜索统计显示
            function updateSearchStats(visible, total) {
                var statsHtml = '<div class="search-stats" style="margin-top: 10px; color: #6c757d; font-size: 12px;">' +
                               '显示 ' + visible + ' / ' + total + ' 条记录</div>';
                $('.basic-search-container .search-stats').remove();
                $('.basic-search-container').append(statsHtml);
            }
        }

        // 昨日小计切换函数 (保持向后兼容)
        function toggleData(event) {
            // 这个函数现在由 datadiff-enhanced.js 中的增强版本处理
            if (window.dataDiffEnhancer && window.dataDiffEnhancer.toggleData) {
                return window.dataDiffEnhancer.toggleData(event);
            }

            // 降级处理
            event.preventDefault();
            const dataSpan = document.getElementById('data');

            if (dataSpan.style.display !== 'none') {
                $(dataSpan).slideUp(300);
                return;
            }

            if (dataLoaded) {
                $(dataSpan).slideDown(300);
                return;
            }

            // 简单的加载处理
            fetch('/fwyytool/arkgo/tool/getdiffcount')
                .then(response => response.json())
                .then(data => {
                    if (data.errNo === 0 && data.data) {
                        const statsData = data.data;
                        const sortableData = Object.entries(statsData).map(([key, value]) => ({
                            name: key,
                            ...value
                        }));

                        sortableData.sort((a, b) => b.hasDiffCnt - a.hasDiffCnt);

                        let tableHtml = '<table class="yesterday-stats-table">';
                        tableHtml += '<thead><tr><th>接口名称</th><th>有差异</th><th>无差异</th><th>未完成</th><th>失败</th></tr></thead><tbody>';

                        sortableData.forEach((item, index) => {
                            const rowClass = index % 2 === 0 ? 'even' : 'odd';
                            const diffClass = item.hasDiffCnt > 0 ? 'has-diff' : '';

                            tableHtml += `<tr class="${rowClass}">`;
                            tableHtml += `<td>${item.name}</td>`;
                            tableHtml += `<td class="${diffClass}">${item.hasDiffCnt}</td>`;
                            tableHtml += `<td>${item.noDiffCnt}</td>`;
                            tableHtml += `<td>${item.unFinishCnt}</td>`;
                            tableHtml += `<td>${item.failedCnt}</td>`;
                            tableHtml += '</tr>';
                        });

                        tableHtml += '</tbody></table>';
                        dataSpan.innerHTML = tableHtml;
                    } else {
                        const formattedJSON = JSON.stringify(data, null, 4);
                        dataSpan.innerHTML = '<pre class="json-error">' + formattedJSON + '</pre>';
                    }

                    $(dataSpan).hide().slideDown(300);
                    dataLoaded = true;
                })
                .catch(error => {
                    dataSpan.innerHTML = '<div class="error-container"><div class="error-message">获取数据失败: ' + error.message + '</div></div>';
                    $(dataSpan).hide().slideDown(300);
                });
        }
    </script>
</body>
</html>
{{end}}


